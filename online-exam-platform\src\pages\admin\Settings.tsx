import { useState } from 'react';
import { EyeIcon, EyeOffIcon, RefreshCwIcon, DownloadIcon, UploadIcon, LockIcon, ShieldCheckIcon, ArchiveIcon } from 'lucide-react';

const Settings = () => {
  const [settings, setSettings] = useState({
    providerApiKey: '',
    providerApiKeyVisible: false,
    backupApiKey: '',
    backupApiKeyVisible: false,
    dataRetentionDays: 30,
    logRetentionDays: 90,
    complianceSettings: {
      gdprCompliance: true,
      hipaaCompliance: false,
      autoDataPurge: true,
      encryptionAtRest: true
    },
    backupSettings: {
      autoBackup: true,
      backupFrequency: 'weekly',
      backupLocation: 'aws_s3'
    }
  });

  const [activeTab, setActiveTab] = useState('apiKeys');
  const [isRotatingKey, setIsRotatingKey] = useState(false);
  const [showKeyModal, setShowKeyModal] = useState(false);
  const [newKey, setNewKey] = useState('');

  const rotateApiKey = () => {
    setIsRotatingKey(true);
    // Simulate API call
    setTimeout(() => {
      const randomKey = [...Array(32)].map(() => Math.random().toString(36)[2]).join('');
      setSettings({...settings, providerApiKey: randomKey});
      setNewKey(randomKey);
      setShowKeyModal(true);
      setIsRotatingKey(false);
    }, 1000);
  };

  const handleSave = () => {
    // Simulate save action
    alert('Settings saved successfully');
  };

interface ComplianceSettings {
    gdprCompliance: boolean;
    hipaaCompliance: boolean;
    autoDataPurge: boolean;
    encryptionAtRest: boolean;
}

interface BackupSettings {
    autoBackup: boolean;
    backupFrequency: string;
    backupLocation: string;
}

interface SettingsState {
    providerApiKey: string;
    providerApiKeyVisible: boolean;
    backupApiKey: string;
    backupApiKeyVisible: boolean;
    dataRetentionDays: number;
    logRetentionDays: number;
    complianceSettings: ComplianceSettings;
    backupSettings: BackupSettings;
}

type KeyType = 'provider' | 'backup';

const toggleKeyVisibility = (keyType: KeyType) => {
    if (keyType === 'provider') {
        setSettings({...settings, providerApiKeyVisible: !settings.providerApiKeyVisible});
    } else {
        setSettings({...settings, backupApiKeyVisible: !settings.backupApiKeyVisible});
    }
};

  return (
    <div className="settings-container">
      <div className="settings-content">
        <h1>System Settings</h1>
        
        <div className="settings-tabs">
          <button 
            className={`tab-button ${activeTab === 'apiKeys' ? 'active' : ''}`}
            onClick={() => setActiveTab('apiKeys')}
          >
            <LockIcon className="icon" />
            API Keys
          </button>
          <button 
            className={`tab-button ${activeTab === 'retention' ? 'active' : ''}`}
            onClick={() => setActiveTab('retention')}
          >
            <ArchiveIcon className="icon" />
            Retention
          </button>
          <button 
            className={`tab-button ${activeTab === 'compliance' ? 'active' : ''}`}
            onClick={() => setActiveTab('compliance')}
          >
            <ShieldCheckIcon className="icon" />
            Compliance
          </button>
        </div>

        <div className="settings-panel">
          {activeTab === 'apiKeys' && (
            <div className="api-keys-section">
              <div className="setting-group">
                <h2>Provider API Keys</h2>
                <div className="input-group">
                  <label>Primary API Key</label>
                  <div className="password-input">
                    <input
                      type={settings.providerApiKeyVisible ? 'text' : 'password'}
                      value={settings.providerApiKey}
                      onChange={(e) => setSettings({...settings, providerApiKey: e.target.value})}
                      placeholder="Enter provider API key"
                    />
                    <button 
                      className="toggle-visibility"
                      onClick={() => toggleKeyVisibility('provider')}
                    >
                      {settings.providerApiKeyVisible ? <EyeOffIcon className="icon" /> : <EyeIcon className="icon" />}
                    </button>
                  </div>
                  <button 
                    className="rotate-button"
                    onClick={rotateApiKey}
                    disabled={isRotatingKey}
                  >
                    <RefreshCwIcon className="icon" />
                    {isRotatingKey ? 'Rotating...' : 'Rotate Key'}
                  </button>
                </div>

                <div className="input-group">
                  <label>Backup API Key</label>
                  <div className="password-input">
                    <input
                      type={settings.backupApiKeyVisible ? 'text' : 'password'}
                      value={settings.backupApiKey}
                      onChange={(e) => setSettings({...settings, backupApiKey: e.target.value})}
                      placeholder="Enter backup API key"
                    />
                    <button 
                      className="toggle-visibility"
                      onClick={() => toggleKeyVisibility('backup')}
                    >
                      {settings.backupApiKeyVisible ? <EyeOffIcon className="icon" /> : <EyeIcon className="icon" />}
                    </button>
                  </div>
                </div>
              </div>

              <div className="setting-group">
                <h2>Key Management</h2>
                <div className="key-actions">
                  <button className="action-button">
                    <DownloadIcon className="icon" />
                    Export Keys
                  </button>
                  <button className="action-button">
                    <UploadIcon className="icon" />
                    Import Keys
                  </button>
                </div>
                <p className="note">
                  <strong>Note:</strong> API keys provide full access to your account. Keep them secure and rotate regularly.
                </p>
              </div>
            </div>
          )}

          {activeTab === 'retention' && (
            <div className="retention-section">
              <div className="setting-group">
                <h2>Data Retention Policies</h2>
                <div className="input-group">
                  <label>User Data Retention (days)</label>
                  <input
                    type="number"
                    value={settings.dataRetentionDays}
                    onChange={(e) => setSettings({...settings, dataRetentionDays: parseInt(e.target.value) || 0})}
                    min="1"
                    max="3650"
                    placeholder="Enter number of days"
                  />
                  <p className="description">
                    How long to keep user data before automatic deletion. Minimum 7 days.
                  </p>
                </div>

                <div className="input-group">
                  <label>System Logs Retention (days)</label>
                  <input
                    type="number"
                    value={settings.logRetentionDays}
                    onChange={(e) => setSettings({...settings, logRetentionDays: parseInt(e.target.value) || 0})}
                    min="1"
                    max="3650"
                    placeholder="Enter number of days"
                    title="System Logs Retention (days)"
                  />
                  <p className="description">
                    How long to keep system logs for auditing purposes.
                  </p>
                </div>
              </div>

              <div className="setting-group">
                <h2>Backup Configuration</h2>
                <div className="checkbox-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={settings.backupSettings.autoBackup}
                      onChange={(e) => setSettings({
                        ...settings, 
                        backupSettings: {
                          ...settings.backupSettings,
                          autoBackup: e.target.checked
                        }
                      })}
                    />
                    Enable Automatic Backups
                  </label>
                </div>

                {settings.backupSettings.autoBackup && (
                  <div className="input-group">
                    <label>Backup Frequency</label>
                    <select
                      id="backup-frequency-select"
                      title="Backup Frequency"
                      value={settings.backupSettings.backupFrequency}
                      onChange={(e) => setSettings({
                        ...settings, 
                        backupSettings: {
                          ...settings.backupSettings,
                          backupFrequency: e.target.value
                        }
                      })}
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                  </div>
                )}

                {settings.backupSettings.autoBackup && (
                  <div className="input-group">
                    <label>Backup Location</label>
                    <select
                      title="Backup Location"
                      value={settings.backupSettings.backupLocation}
                      onChange={(e) => setSettings({
                        ...settings, 
                        backupSettings: {
                          ...settings.backupSettings,
                          backupLocation: e.target.value
                        }
                      })}
                    >
                      <option value="aws_s3">AWS S3</option>
                      <option value="google_cloud">Google Cloud Storage</option>
                      <option value="azure_blob">Azure Blob Storage</option>
                      <option value="self_hosted">Self-Hosted Server</option>
                    </select>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'compliance' && (
            <div className="compliance-section">
              <div className="setting-group">
                <h2>Compliance Standards</h2>
                <div className="checkbox-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={settings.complianceSettings.gdprCompliance}
                      onChange={(e) => setSettings({
                        ...settings, 
                        complianceSettings: {
                          ...settings.complianceSettings,
                          gdprCompliance: e.target.checked
                        }
                      })}
                    />
                    Enable GDPR Compliance
                    <span className="compliance-badge">EU</span>
                  </label>
                  <p className="description">
                    Ensures compliance with General Data Protection Regulation requirements.
                  </p>
                </div>

                <div className="checkbox-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={settings.complianceSettings.hipaaCompliance}
                      onChange={(e) => setSettings({
                        ...settings, 
                        complianceSettings: {
                          ...settings.complianceSettings,
                          hipaaCompliance: e.target.checked
                        }
                      })}
                    />
                    Enable HIPAA Compliance
                    <span className="compliance-badge">US</span>
                  </label>
                  <p className="description">
                    Required for handling protected health information (PHI) in the United States.
                  </p>
                </div>
              </div>

              <div className="setting-group">
                <h2>Security Settings</h2>
                <div className="checkbox-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={settings.complianceSettings.autoDataPurge}
                      onChange={(e) => setSettings({
                        ...settings, 
                        complianceSettings: {
                          ...settings.complianceSettings,
                          autoDataPurge: e.target.checked
                        }
                      })}
                    />
                    Automatic Data Purge
                  </label>
                  <p className="description">
                    Automatically delete user data when retention period expires.
                  </p>
                </div>

                <div className="checkbox-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={settings.complianceSettings.encryptionAtRest}
                      onChange={(e) => setSettings({
                        ...settings, 
                        complianceSettings: {
                          ...settings.complianceSettings,
                          encryptionAtRest: e.target.checked
                        }
                      })}
                    />
                    Encryption at Rest
                  </label>
                  <p className="description">
                    Encrypt all stored data with AES-256 encryption.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="save-section">
            <button className="save-button" onClick={handleSave}>
              Save All Settings
            </button>
          </div>
        </div>
      </div>

      {/* New Key Modal */}
      {showKeyModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-content">
              <h2>New API Key Generated</h2>
              <p>Please copy this key and store it securely. You won't be able to see it again.</p>
              <div className="key-display">
                <code>{newKey}</code>
                <button 
                  className="copy-button"
                  onClick={() => {
                    navigator.clipboard.writeText(newKey);
                    alert('Key copied to clipboard!');
                  }}
                >
                  Copy to Clipboard
                </button>
              </div>
            </div>
            <div className="modal-footer">
              <button
                onClick={() => setShowKeyModal(false)}
                className="confirm-button"
              >
                I've Saved My Key
              </button>
            </div>
          </div>
        </div>
      )}

      <style>{`
        .settings-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          padding: 2rem;
        }

        .settings-content {
          max-width: 1000px;
          margin: 0 auto;
        }

        .settings-content h1 {
          font-size: 1.75rem;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 1.5rem;
        }

        .settings-tabs {
          display: flex;
          border-bottom: 1px solid #e5e7eb;
          margin-bottom: 1.5rem;
        }

        .tab-button {
          display: flex;
          align-items: center;
          padding: 0.75rem 1.5rem;
          background: transparent;
          border: none;
          border-bottom: 3px solid transparent;
          font-weight: 500;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s;
        }

        .tab-button:hover {
          color: #4f46e5;
        }

        .tab-button.active {
          color: #4f46e5;
          border-bottom-color: #4f46e5;
        }

        .tab-button .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
        }

        .settings-panel {
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(0, 0, 0, 0.05);
          padding: 1.5rem;
        }

        .setting-group {
          margin-bottom: 2rem;
          padding-bottom: 1.5rem;
          border-bottom: 1px solid #f3f4f6;
        }

        .setting-group:last-child {
          border-bottom: none;
          margin-bottom: 0;
          padding-bottom: 0;
        }

        .setting-group h2 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1rem;
        }

        .input-group {
          margin-bottom: 1.25rem;
        }

        .input-group label {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #374151;
          margin-bottom: 0.5rem;
        }

        .input-group input, 
        .input-group select {
          width: 100%;
          max-width: 400px;
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .input-group input:focus, 
        .input-group select:focus {
          outline: none;
          border-color: #818cf8;
          box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
        }

        .input-group .description {
          font-size: 0.75rem;
          color: #6b7280;
          margin-top: 0.25rem;
        }

        .password-input {
          position: relative;
          max-width: 400px;
        }

        .password-input input {
          padding-right: 2.5rem;
          width: 100%;
        }

        .toggle-visibility {
          position: absolute;
          right: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: #6b7280;
          cursor: pointer;
          padding: 0.25rem;
        }

        .toggle-visibility .icon {
          width: 1.25rem;
          height: 1.25rem;
        }

        .rotate-button {
          display: flex;
          align-items: center;
          margin-top: 0.5rem;
          padding: 0.375rem 0.75rem;
          background: #f3f4f6;
          border: none;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          color: #4b5563;
          cursor: pointer;
          transition: all 0.2s;
        }

        .rotate-button:hover {
          background: #e5e7eb;
        }

        .rotate-button .icon {
          width: 1rem;
          height: 1rem;
          margin-right: 0.5rem;
          animation: ${isRotatingKey ? 'spin 1s linear infinite' : 'none'};
        }

        @keyframes spin {
          to { transform: rotate(360deg); }
        }

        .key-actions {
          display: flex;
          gap: 1rem;
          margin-bottom: 1rem;
        }

        .action-button {
          display: flex;
          align-items: center;
          padding: 0.5rem 1rem;
          background: #f3f4f6;
          border: none;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          color: #4b5563;
          cursor: pointer;
          transition: all 0.2s;
        }

        .action-button:hover {
          background: #e5e7eb;
        }

        .action-button .icon {
          width: 1rem;
          height: 1rem;
          margin-right: 0.5rem;
        }

        .note {
          font-size: 0.75rem;
          color: #6b7280;
          margin-top: 1rem;
        }

        .checkbox-group {
          margin-bottom: 1rem;
        }

        .checkbox-group label {
          display: flex;
          align-items: center;
          font-size: 0.875rem;
          color: #374151;
          cursor: pointer;
        }

        .checkbox-group input {
          margin-right: 0.5rem;
        }

        .compliance-badge {
          display: inline-block;
          margin-left: 0.5rem;
          padding: 0.125rem 0.375rem;
          background: #e5e7eb;
          border-radius: 0.25rem;
          font-size: 0.625rem;
          font-weight: 600;
          color: #374151;
          text-transform: uppercase;
        }

        .save-section {
          margin-top: 2rem;
          padding-top: 1.5rem;
          border-top: 1px solid #e5e7eb;
          text-align: right;
        }

        .save-button {
          padding: 0.5rem 1.5rem;
          background: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .save-button:hover {
          background: #4338ca;
        }

        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal {
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          width: 100%;
          max-width: 500px;
          overflow: hidden;
        }

        .modal-content {
          padding: 1.5rem;
        }

        .modal-content h2 {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1rem;
        }

        .modal-content p {
          color: #6b7280;
          margin-bottom: 1.5rem;
        }

        .key-display {
          background: #f9fafb;
          border-radius: 0.375rem;
          padding: 1rem;
          margin-bottom: 1.5rem;
        }

        .key-display code {
          display: block;
          font-family: monospace;
          word-break: break-all;
          margin-bottom: 1rem;
        }

        .copy-button {
          padding: 0.375rem 0.75rem;
          background: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.25rem;
          font-size: 0.875rem;
          cursor: pointer;
          transition: all 0.2s;
        }

        .copy-button:hover {
          background: #4338ca;
        }

        .modal-footer {
          display: flex;
          justify-content: flex-end;
          padding: 1rem 1.5rem;
          background: #f9fafb;
          border-top: 1px solid #e5e7eb;
        }

        .confirm-button {
          padding: 0.5rem 1.5rem;
          background: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .confirm-button:hover {
          background: #4338ca;
        }

        @media (max-width: 768px) {
          .settings-tabs {
            overflow-x: auto;
            padding-bottom: 0.5rem;
          }

          .tab-button {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
          }

          .key-actions {
            flex-direction: column;
          }
        }
      `}</style>
    </div>
  );
};

export default Settings;