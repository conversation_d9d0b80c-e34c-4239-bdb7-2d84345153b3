import React, { useState } from 'react';
import { 
  User,
  Edit,
  X,
  Save,
  Lock,
  Mail,
  Phone,
  School,
  BookOpen,
  Calendar
} from 'lucide-react';

const Profile = () => {
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    institution: 'University of Example',
    department: 'Computer Science',
    year: '3rd Year',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setEditMode(false);
    // Normally, send formData to backend here
  };

  return (
    <div className="profile-page">
      <div className="profile-content">
        <div className="profile-header">
          <h1 className="profile-title">
            <User size={28} className="title-icon" />
            Profile
          </h1>
          {!editMode && (
            <button 
              onClick={() => setEditMode(true)}
              className="edit-button"
            >
              <Edit size={16} className="button-icon" />
              Edit Profile
            </button>
          )}
        </div>

        <div className="profile-card">
          <div className="profile-header-section">
            <div className="avatar">
              {formData.firstName.charAt(0)}{formData.lastName.charAt(0)}
            </div>
            <div className="profile-info">
              <h2 className="profile-name">
                {formData.firstName} {formData.lastName}
              </h2>
              <p className="profile-email">
                <Mail size={16} className="info-icon" />
                {formData.email}
              </p>
              <p className="profile-institution">
                <School size={16} className="info-icon" />
                {formData.institution}
              </p>
            </div>
          </div>

          {editMode ? (
            <form onSubmit={handleSubmit} className="edit-form">
              <div className="form-grid">
                <div className="form-group">
                  <label htmlFor="firstName" className="form-label">
                    First Name
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="lastName" className="form-label">
                    Last Name
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="email" className="form-label">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="phone" className="form-label">
                    Phone
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="institution" className="form-label">
                    Institution
                  </label>
                  <input
                    type="text"
                    id="institution"
                    name="institution"
                    value={formData.institution}
                    onChange={handleChange}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="department" className="form-label">
                    Department
                  </label>
                  <input
                    type="text"
                    id="department"
                    name="department"
                    value={formData.department}
                    onChange={handleChange}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="year" className="form-label">
                    Year
                  </label>
                  <select
                    id="year"
                    name="year"
                    value={formData.year}
                    onChange={handleChange}
                    className="form-input"
                  >
                    <option>1st Year</option>
                    <option>2nd Year</option>
                    <option>3rd Year</option>
                    <option>4th Year</option>
                    <option>5th Year</option>
                    <option>Graduate</option>
                  </select>
                </div>
              </div>
              <div className="form-actions">
                <button
                  type="button"
                  onClick={() => setEditMode(false)}
                  className="cancel-button"
                >
                  <X size={16} className="button-icon" />
                  Cancel
                </button>
                <button
                  type="submit"
                  className="save-button"
                >
                  <Save size={16} className="button-icon" />
                  Save Changes
                </button>
              </div>
            </form>
          ) : (
            <div className="profile-details">
              <div className="details-grid">
                <div className="detail-item">
                  <h3 className="detail-label">Full Name</h3>
                  <p className="detail-value">
                    {formData.firstName} {formData.lastName}
                  </p>
                </div>
                <div className="detail-item">
                  <h3 className="detail-label">Email</h3>
                  <p className="detail-value">
                    <Mail size={16} className="detail-icon" />
                    {formData.email}
                  </p>
                </div>
                <div className="detail-item">
                  <h3 className="detail-label">Phone</h3>
                  <p className="detail-value">
                    <Phone size={16} className="detail-icon" />
                    {formData.phone}
                  </p>
                </div>
                <div className="detail-item">
                  <h3 className="detail-label">Institution</h3>
                  <p className="detail-value">
                    <School size={16} className="detail-icon" />
                    {formData.institution}
                  </p>
                </div>
                <div className="detail-item">
                  <h3 className="detail-label">Department</h3>
                  <p className="detail-value">
                    <BookOpen size={16} className="detail-icon" />
                    {formData.department}
                  </p>
                </div>
                <div className="detail-item">
                  <h3 className="detail-label">Year</h3>
                  <p className="detail-value">
                    <Calendar size={16} className="detail-icon" />
                    {formData.year}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Change Password Section */}
        <div className="password-card">
          <div className="password-header">
            <Lock size={20} className="password-icon" />
            <h2 className="password-title">Change Password</h2>
          </div>
          <form className="password-form">
            <div className="form-group">
              <label htmlFor="current-password" className="form-label">
                Current Password
              </label>
              <input
                id="current-password"
                name="current-password"
                type="password"
                autoComplete="current-password"
                required
                className="form-input"
              />
            </div>
            <div className="form-group">
              <label htmlFor="new-password" className="form-label">
                New Password
              </label>
              <input
                id="new-password"
                name="new-password"
                type="password"
                autoComplete="new-password"
                required
                className="form-input"
              />
            </div>
            <div className="form-group">
              <label htmlFor="confirm-password" className="form-label">
                Confirm New Password
              </label>
              <input
                id="confirm-password"
                name="confirm-password"
                type="password"
                autoComplete="new-password"
                required
                className="form-input"
              />
            </div>
            <div className="form-actions">
              <button
                type="submit"
                className="update-button"
              >
                Update Password
              </button>
            </div>
          </form>
        </div>
      </div>

      <style>{`
        .profile-page {
          display: flex;
          min-height: 100vh;
          background-color: #f8fafc;
        }

        .profile-content {
          flex: 1;
          padding: 2rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .profile-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
        }

        .profile-title {
          display: flex;
          align-items: center;
          font-size: 2rem;
          font-weight: 700;
          color: #1e293b;
          gap: 0.75rem;
        }

        .title-icon {
          color: #4f46e5;
        }

        .edit-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          background-color: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .edit-button:hover {
          background-color: #4338ca;
        }

        .button-icon {
          margin-right: 0.25rem;
        }

        .profile-card, .password-card {
          background-color: white;
          border-radius: 0.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          margin-bottom: 1.5rem;
          overflow: hidden;
        }

        .profile-header-section {
          display: flex;
          align-items: center;
          padding: 1.5rem;
          border-bottom: 1px solid #e2e8f0;
        }

        .avatar {
          width: 5rem;
          height: 5rem;
          border-radius: 9999px;
          background-color: #e0e7ff;
          color: #4f46e5;
          font-size: 1.5rem;
          font-weight: 700;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }

        .profile-info {
          margin-left: 1.5rem;
        }

        .profile-name {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 0.5rem 0;
        }

        .profile-email, .profile-institution {
          display: flex;
          align-items: center;
          color: #64748b;
          margin: 0.25rem 0;
          font-size: 0.875rem;
        }

        .info-icon {
          margin-right: 0.5rem;
          color: #64748b;
        }

        .edit-form, .password-form {
          padding: 1.5rem;
        }

        .form-grid, .details-grid {
          display: grid;
          grid-template-columns: repeat(1, 1fr);
          gap: 1.5rem;
          margin-bottom: 1.5rem;
        }

        @media (min-width: 768px) {
          .form-grid, .details-grid {
            grid-template-columns: repeat(2, 1fr);
          }
        }

        .form-group {
          margin-bottom: 1rem;
        }

        .form-label {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #374151;
          margin-bottom: 0.5rem;
        }

        .form-input {
          width: 100%;
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .form-input:focus {
          outline: none;
          border-color: #818cf8;
          box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
        }

        .form-actions {
          display: flex;
          justify-content: flex-end;
          gap: 0.75rem;
        }

        .cancel-button {
          display: flex;
          align-items: center;
          padding: 0.5rem 1rem;
          background-color: white;
          color: #374151;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .cancel-button:hover {
          background-color: #f9fafb;
          border-color: #9ca3af;
        }

        .save-button, .update-button {
          padding: 0.5rem 1rem;
          background-color: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .save-button:hover, .update-button:hover {
          background-color: #4338ca;
        }

        .profile-details {
          padding: 1.5rem;
        }

        .detail-item {
          margin-bottom: 1rem;
        }

        .detail-label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #64748b;
          margin-bottom: 0.25rem;
        }

        .detail-value {
          display: flex;
          align-items: center;
          font-size: 0.875rem;
          color: #1e293b;
        }

        .detail-icon {
          margin-right: 0.5rem;
          color: #64748b;
        }

        .password-header {
          display: flex;
          align-items: center;
          padding: 1.5rem;
          border-bottom: 1px solid #e2e8f0;
        }

        .password-icon {
          color: #4f46e5;
          margin-right: 0.75rem;
        }

        .password-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1e293b;
          margin: 0;
        }
      `}</style>
    </div>
  );
};

export default Profile;