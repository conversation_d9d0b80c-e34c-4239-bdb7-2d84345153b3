import React, { useState, useEffect } from 'react';
import { 
  <PERSON>,
  Mic,
  Wifi,
  CheckCircle,
  XCircle,
  Loader2,
  Play,
  StopCircle
} from 'lucide-react';

// Extend the Navigator type to include 'connection'
interface NetworkInformation extends EventTarget {
  readonly downlink: number;
  readonly effectiveType: string;
  readonly rtt: number;
  readonly saveData: boolean;
}
interface NavigatorWithConnection extends Navigator {
  connection?: NetworkInformation;
  mozConnection?: NetworkInformation;
  webkitConnection?: NetworkInformation;
}

const DeviceCheck = () => {
  const [cameraStatus, setCameraStatus] = useState('Not checked');
  const [micStatus, setMicStatus] = useState('Not checked');
  const [networkStatus, setNetworkStatus] = useState('Not checked');
  const [isChecking, setIsChecking] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Clean up media stream when component unmounts
  useEffect(() => {
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [stream]);

  const checkCamera = async () => {
    setCameraStatus('Checking...');
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({ video: true });
      setStream(mediaStream);
      setCameraStatus('Working');
      setShowPreview(true);
      return true;
    } catch (error) {
      console.error('Camera access error:', error);
      if (error instanceof Error) {
        setCameraStatus(`Failed: ${error.message}`);
      } else {
        setCameraStatus('Failed: Unknown error');
      }
      return false;
    }
  };

  const checkMicrophone = async () => {
    setMicStatus('Checking...');
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      // Test if we're getting audio levels
      const audioContext = new ((window.AudioContext || (window as any).webkitAudioContext))();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(mediaStream);
      microphone.connect(analyser);
      
      if (!stream) {
        setStream(mediaStream);
      } else {
        mediaStream.getTracks().forEach(track => track.stop());
      }
      
      setMicStatus('Working');
      return true;
    } catch (error) {
      console.error('Microphone access error:', error);
      if (error instanceof Error) {
        setMicStatus(`Failed: ${error.message}`);
      } else {
        setMicStatus('Failed: Unknown error');
      }
      return false;
    }
  };

  const checkNetwork = async () => {
    setNetworkStatus('Checking...');
    try {
      const nav = navigator as NavigatorWithConnection;
      const connection = nav.connection || nav.mozConnection || nav.webkitConnection;
      
      let networkInfo = '';
      if (connection) {
        networkInfo = `Type: ${connection.effectiveType}, RTT: ${connection.rtt}ms`;
      }
      
      const startTime = Date.now();
      await fetch('https://www.google.com', { mode: 'no-cors' });
      const endTime = Date.now();
      const pingTime = endTime - startTime;
      
      setNetworkStatus(`Connected (Ping: ${pingTime}ms ${networkInfo ? '| ' + networkInfo : ''})`);
      return true;
    } catch (error) {
      console.error('Network check error:', error);
      if (error instanceof Error) {
        setNetworkStatus(`Failed: ${error.message}`);
      } else {
        setNetworkStatus('Failed: Unknown error');
      }
      return false;
    }
  };

  const handleCheck = async () => {
    setIsChecking(true);
    setShowPreview(false);
    
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    
    await checkCamera();
    await checkMicrophone();
    await checkNetwork();
    
    setIsChecking(false);
  };

  const stopAllChecks = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setShowPreview(false);
  };

  const getStatusIcon = (status: string) => {
    if (status === 'Working' || status.includes('Connected')) {
      return <CheckCircle size={16} className="text-green-500" />;
    }
    if (status.includes('Failed')) {
      return <XCircle size={16} className="text-red-500" />;
    }
    if (status === 'Checking...') {
      return <Loader2 size={16} className="text-yellow-500 animate-spin" />;
    }
    return null;
  };

  return (
    <div className="device-check-page">
      <div className="device-check-card">
        <div className="device-check-header">
          <h2 className="device-check-title">Device Check</h2>
          <p className="device-check-subtitle">
            Ensure your camera, microphone, and internet connection are working properly.
          </p>
        </div>
        
        {showPreview && stream && (
          <div className="camera-preview">
            <h3 className="preview-title">Camera Preview</h3>
            <video 
              autoPlay 
              playsInline
              muted
              ref={video => {
                if (video && stream) {
                  video.srcObject = stream;
                }
              }}
              className="preview-video"
            />
          </div>
        )}
        
        <div className="status-list">
          <div className="status-item">
            <div className="status-info">
              <Camera size={20} className="status-icon" />
              <span className="status-label">Camera:</span>
            </div>
            <div className={`status-value ${getStatusClass(cameraStatus)}`}>
              {getStatusIcon(cameraStatus)}
              <span>{cameraStatus}</span>
            </div>
          </div>
          
          <div className="status-item">
            <div className="status-info">
              <Mic size={20} className="status-icon" />
              <span className="status-label">Microphone:</span>
            </div>
            <div className={`status-value ${getStatusClass(micStatus)}`}>
              {getStatusIcon(micStatus)}
              <span>{micStatus}</span>
            </div>
          </div>
          
          <div className="status-item">
            <div className="status-info">
              <Wifi size={20} className="status-icon" />
              <span className="status-label">Network:</span>
            </div>
            <div className={`status-value ${getStatusClass(networkStatus)}`}>
              {getStatusIcon(networkStatus)}
              <span>{networkStatus}</span>
            </div>
          </div>
        </div>
        
        <div className="action-buttons">
          <button
            onClick={handleCheck}
            disabled={isChecking}
            className={`primary-button ${isChecking ? 'disabled' : ''}`}
          >
            {isChecking ? (
              <>
                <Loader2 size={18} className="animate-spin button-icon" />
                Checking...
              </>
            ) : (
              <>
                <Play size={18} className="button-icon" />
                Run Device Check
              </>
            )}
          </button>
          
          {(stream || showPreview) && (
            <button
              onClick={stopAllChecks}
              className="secondary-button"
            >
              <StopCircle size={18} className="button-icon" />
              Stop Checks
            </button>
          )}
        </div>
      </div>

      <style>{`
        .device-check-page {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 1.5rem;
          background-color: #f8fafc;
        }

        .device-check-card {
          width: 100%;
          max-width: 28rem;
          background-color: white;
          border-radius: 0.75rem;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          padding: 2rem;
        }

        .device-check-header {
          margin-bottom: 1.5rem;
          text-align: center;
        }

        .device-check-title {
          font-size: 1.875rem;
          font-weight: 700;
          color: #15803d;
          margin-bottom: 0.5rem;
        }

        .device-check-subtitle {
          color: #64748b;
          line-height: 1.5;
        }

        .camera-preview {
          margin-bottom: 1.5rem;
        }

        .preview-title {
          font-size: 1rem;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 0.5rem;
        }

        .preview-video {
          width: 100%;
          height: 12rem;
          background-color: #0f172a;
          border-radius: 0.5rem;
          object-fit: cover;
        }

        .status-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          margin-bottom: 2rem;
        }

        .status-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .status-info {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .status-icon {
          color: #64748b;
        }

        .status-label {
          font-weight: 500;
          color: #1e293b;
        }

        .status-value {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.875rem;
        }

        .status-value.success {
          color: #15803d;
        }

        .status-value.error {
          color: #dc2626;
        }

        .status-value.warning {
          color: #d97706;
        }

        .status-value.neutral {
          color: #64748b;
        }

        .action-buttons {
          display: flex;
          gap: 0.75rem;
        }

        .primary-button {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          padding: 0.75rem 1.5rem;
          background-color: #15803d;
          color: white;
          border: none;
          border-radius: 0.5rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .primary-button:hover:not(.disabled) {
          background-color: #166534;
          transform: translateY(-1px);
        }

        .primary-button.disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .secondary-button {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          padding: 0.75rem 1.5rem;
          background-color: white;
          color: #15803d;
          border: 1px solid #15803d;
          border-radius: 0.5rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .secondary-button:hover {
          background-color: #f0fdf4;
          transform: translateY(-1px);
        }

        .button-icon {
          margin-right: 0.25rem;
        }

        @media (max-width: 640px) {
          .action-buttons {
            flex-direction: column;
          }
        }
      `}</style>
    </div>
  );
};

// Helper function to determine status class
function getStatusClass(status: string): string {
  if (status === 'Working' || status.includes('Connected')) return 'success';
  if (status.includes('Failed')) return 'error';
  if (status === 'Checking...') return 'warning';
  return 'neutral';
}

export default DeviceCheck;