import { NavLink } from 'react-router-dom';
import {
  Monitor,
  Eye,
  Flag,
  LogOut
} from 'lucide-react';

const ProctorSidebar = () => {
  const menuItems = [
    { label: 'Dashboard', icon: <Monitor size={18} />, to: '/proctor/dashboard' },
    { label: 'Flag Review', icon: <Flag size={18} />, to: '/proctor/flags' },
    { label: 'Logout', icon: <LogOut size={18} />, to: '/login' },
  ];

  return (
    <div className="sidebar-container">
      <div className="sidebar-header">
        <h1 className="sidebar-title">Proctor Panel</h1>
      </div>
      <nav className="sidebar-nav">
        {menuItems.map((item, idx) => (
          <NavLink
            key={idx}
            to={item.to}
            className={({ isActive }) => `sidebar-link ${isActive ? 'active' : ''}`}
          >
            <span className="sidebar-icon">{item.icon}</span>
            <span className="sidebar-label">{item.label}</span>
          </NavLink>
        ))}
      </nav>

      <style>{`
        .sidebar-container {
          width: 16rem;
          background-color: #1e293b;
          color: white;
          min-height: 100vh;
          display: flex;
          flex-direction: column;
        }
        .sidebar-header {
          padding: 1.5rem;
          border-bottom: 1px solid #334155;
        }
        .sidebar-title {
          font-size: 1.25rem;
          font-weight: 700;
        }
        .sidebar-nav {
          flex: 1;
          padding: 0.5rem 0;
          display: flex;
          flex-direction: column;
        }
        .sidebar-link {
          display: flex;
          align-items: center;
          padding: 0.75rem 1.5rem;
          margin: 0.25rem 0.5rem;
          border-radius: 0.375rem;
          color: #e2e8f0;
          text-decoration: none;
          transition: all 0.2s ease;
        }
        .sidebar-link:hover {
          background-color: #334155;
          color: white;
        }
        .sidebar-link.active {
          background-color: #4f46e5;
          color: white;
        }
        .sidebar-icon {
          margin-right: 0.75rem;
        }
        .sidebar-link[href="/login"] {
          margin-top: auto;
          margin-bottom: 1rem;
          color: #f87171;
        }
        .sidebar-link[href="/login"]:hover {
          background-color: #7f1d1d;
          color: #fecaca;
        }
      `}</style>
    </div>
  );
};

export default ProctorSidebar;
