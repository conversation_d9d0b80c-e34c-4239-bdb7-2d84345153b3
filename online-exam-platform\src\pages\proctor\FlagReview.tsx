import { 
  AlertCircle, 
  CheckCircle, 
  XCircle, 
  Video, 
  Edit2, 
  Download, 
  ChevronDown,
  ChevronRight,
  Flag,
  MoreVertical
} from 'lucide-react';
import { useState } from 'react';

const FlagReview = () => {
  const [flags, setFlags] = useState([
    { 
      id: 1, 
      time: '00:10', 
      timestamp: 10,
      reason: 'No face detected', 
      severity: 'high',
      status: 'unreviewed',
      notes: '',
      videoClip: 'clip1.mp4',
      screenshots: ['ss1.jpg', 'ss2.jpg']
    },
    { 
      id: 2, 
      time: '00:25', 
      timestamp: 25,
      reason: 'Multiple people', 
      severity: 'high',
      status: 'reviewed',
      notes: 'Verified second person in frame',
      videoClip: 'clip2.mp4',
      screenshots: ['ss3.jpg']
    },
    { 
      id: 3, 
      time: '00:45', 
      timestamp: 45,
      reason: 'Mobile device usage', 
      severity: 'medium',
      status: 'unreviewed',
      notes: '',
      videoClip: 'clip3.mp4',
      screenshots: ['ss4.jpg', 'ss5.jpg']
    },
    { 
      id: 4, 
      time: '01:15', 
      timestamp: 75,
      reason: 'Background noise', 
      severity: 'low',
      status: 'dismissed',
      notes: 'Just keyboard typing',
      videoClip: 'clip4.mp4',
      screenshots: []
    },
  ]);

  const [expandedFlag, setExpandedFlag] = useState<number | null>(null);
  const [newNote, setNewNote] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [sortBy, setSortBy] = useState('time');

  const toggleFlagExpansion = (id: number) => {
    setExpandedFlag(expandedFlag === id ? null : id);
  };

  const updateFlagStatus = (id: number, status: string) => {
    setFlags(flags.map(flag => 
      flag.id === id ? { ...flag, status } : flag
    ));
  };

  const addNote = (id: number) => {
    if (newNote.trim()) {
      setFlags(flags.map(flag => 
        flag.id === id ? { ...flag, notes: newNote } : flag
      ));
      setNewNote('');
    }
  };

  const filteredFlags = flags.filter(flag => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'unreviewed') return flag.status === 'unreviewed';
    if (activeFilter === 'reviewed') return flag.status === 'reviewed';
    if (activeFilter === 'dismissed') return flag.status === 'dismissed';
    return true;
  });

  type Severity = 'high' | 'medium' | 'low';
  const sortedFlags = [...filteredFlags].sort((a, b) => {
    if (sortBy === 'time') return a.timestamp - b.timestamp;
    if (sortBy === 'severity') {
      const severityOrder: Record<Severity, number> = { high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity as Severity] - severityOrder[a.severity as Severity];
    }
    return 0;
  });

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-amber-100 text-amber-800';
      case 'low': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'reviewed': return <CheckCircle size={16} className="text-green-500" />;
      case 'dismissed': return <XCircle size={16} className="text-gray-500" />;
      default: return <AlertCircle size={16} className="text-red-500" />;
    }
  };

    const [editingNoteId, setEditingNoteId] = useState<number | null>(null);

  return (
    <div className="flag-review-page">
      <div className="flag-review-header">
        <h1 className="flag-review-title">
          <Flag size={28} className="header-icon" />
          Flag Review
        </h1>
        <div className="flag-review-controls">
          <div className="filter-controls">
            <button 
              className={`filter-button ${activeFilter === 'all' ? 'active' : ''}`}
              onClick={() => setActiveFilter('all')}
            >
              All ({flags.length})
            </button>
            <button 
              className={`filter-button ${activeFilter === 'unreviewed' ? 'active' : ''}`}
              onClick={() => setActiveFilter('unreviewed')}
            >
              Unreviewed ({flags.filter(f => f.status === 'unreviewed').length})
            </button>
            <button 
              className={`filter-button ${activeFilter === 'reviewed' ? 'active' : ''}`}
              onClick={() => setActiveFilter('reviewed')}
            >
              Reviewed ({flags.filter(f => f.status === 'reviewed').length})
            </button>
            <button 
              className={`filter-button ${activeFilter === 'dismissed' ? 'active' : ''}`}
              onClick={() => setActiveFilter('dismissed')}
            >
              Dismissed ({flags.filter(f => f.status === 'dismissed').length})
            </button>
          </div>
          <div className="sort-control">
            <label htmlFor="sort-by">Sort by:</label>
            <select
              id="sort-by"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
            >
              <option value="time">Time</option>
              <option value="severity">Severity</option>
            </select>
          </div>
        </div>
      </div>

      <div className="flag-list-container">
        {sortedFlags.length === 0 ? (
          <div className="empty-state">
            <CheckCircle size={48} className="text-green-500 mx-auto" />
            <p>No flags found matching your criteria</p>
          </div>
        ) : (
          <ul className="flag-list">
            {sortedFlags.map(flag => (
              <li key={flag.id} className={`flag-item ${flag.status}`}>
                <div 
                  className="flag-summary"
                  onClick={() => toggleFlagExpansion(flag.id)}
                >
                  <div className="flag-time-severity">
                    <span className="flag-time">{flag.time}</span>
                    <span className={`severity-badge ${getSeverityColor(flag.severity)}`}>
                      {flag.severity}
                    </span>
                    {getStatusIcon(flag.status)}
                  </div>
                  <div className="flag-reason">
                    {flag.reason}
                  </div>
                  <button className="expand-button">
                    {expandedFlag === flag.id ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
                  </button>
                </div>

                {expandedFlag === flag.id && (
                  <div className="flag-details">
                    <div className="media-section">
                      <h3 className="section-title">
                        <Video size={18} className="mr-1" />
                        Media Evidence
                      </h3>
                      <div className="media-grid">
                        <div className="video-preview">
                          <div className="video-placeholder">
                            <Video size={24} />
                            <span>Clip: {flag.videoClip}</span>
                          </div>
                          <button className="play-button">
                            Play Segment
                          </button>
                        </div>
                        {flag.screenshots.length > 0 && (
                          <div className="screenshots">
                            {flag.screenshots.map((ss, idx) => (
                              <div key={idx} className="screenshot-thumbnail">
                                <img 
                                  src={`https://via.placeholder.com/150?text=Screenshot+${idx+1}`} 
                                  alt={`Screenshot ${idx+1}`}
                                />
                                <button className="download-button" title="Download screenshot">
                                  <Download size={14} />
                                </button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="annotation-section">
                      <h3 className="section-title">
                        <Edit2 size={18} className="mr-1" />
                        Annotation
                      </h3>
                      {(editingNoteId === flag.id || !flag.notes) ? (
  <div className="add-notes">
    <textarea
      value={newNote}
      onChange={(e) => setNewNote(e.target.value)}
      placeholder="Add your notes about this flag..."
      rows={3}
    ></textarea>
    <button 
      className="save-notes-button"
      onClick={() => {
        addNote(flag.id);
        setEditingNoteId(null); // Stop editing
      }}
      disabled={!newNote.trim()}
    >
      Save Notes
    </button>
  </div>
) : (
  <div className="existing-notes">
    <p>{flag.notes}</p>
    <button 
      className="edit-notes-button"
      onClick={() => {
        setNewNote(flag.notes);
        setEditingNoteId(flag.id);
      }}
    >
      Edit Notes
    </button>
  </div>
)}

                    </div>

                    <div className="flag-actions">
                      <div className="status-actions">
                        <span className="action-label">Mark as:</span>
                        <button 
                          className={`status-button reviewed ${flag.status === 'reviewed' ? 'active' : ''}`}
                          onClick={() => updateFlagStatus(flag.id, 'reviewed')}
                        >
                          <CheckCircle size={16} className="mr-1" />
                          Reviewed
                        </button>
                        <button 
                          className={`status-button dismissed ${flag.status === 'dismissed' ? 'active' : ''}`}
                          onClick={() => updateFlagStatus(flag.id, 'dismissed')}
                        >
                          <XCircle size={16} className="mr-1" />
                          Dismissed
                        </button>
                      </div>
                      <div className="utility-actions">
                        <button className="utility-button">
                          <Download size={16} className="mr-1" />
                          Export
                        </button>
                        <button className="utility-button" title="More options">
                          <MoreVertical size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>

      <style>{`
        .flag-review-page {
          padding: 2rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .flag-review-header {
          margin-bottom: 2rem;
        }

        .flag-review-title {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          font-size: 1.875rem;
          font-weight: 700;
          color: #15803d;
          margin-bottom: 0.5rem;
        }

        .header-icon {
          color: #15803d;
        }

        .flag-review-controls {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 1rem;
          margin-top: 1.5rem;
        }

        .filter-controls {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;
        }

        .filter-button {
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
          font-weight: 500;
          font-size: 0.875rem;
          background-color: #f3f4f6;
          color: #4b5563;
          border: none;
          cursor: pointer;
          transition: all 0.2s;
        }

        .filter-button:hover {
          background-color: #e5e7eb;
        }

        .filter-button.active {
          background-color: #15803d;
          color: white;
        }

        .sort-control {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .sort-control label {
          font-size: 0.875rem;
          color: #4b5563;
        }

        .sort-control select {
          padding: 0.5rem;
          border-radius: 0.375rem;
          border: 1px solid #d1d5db;
          background-color: white;
        }

        .flag-list-container {
          background-color: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }

        .empty-state {
          padding: 3rem;
          text-align: center;
          color: #64748b;
        }

        .empty-state p {
          margin-top: 1rem;
          font-size: 1.125rem;
        }

        .flag-list {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .flag-item {
          border-bottom: 1px solid #e5e7eb;
        }

        .flag-item:last-child {
          border-bottom: none;
        }

        .flag-summary {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem 1.5rem;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .flag-summary:hover {
          background-color: #f8fafc;
        }

        .flag-time-severity {
          display: flex;
          align-items: center;
          gap: 1rem;
          min-width: 180px;
        }

        .flag-time {
          font-weight: 500;
          color: #1f2937;
          width: 50px;
        }

        .severity-badge {
          padding: 0.25rem 0.5rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 500;
          text-transform: capitalize;
        }

        .flag-reason {
          flex-grow: 1;
          padding: 0 1rem;
          color: #1f2937;
        }

        .expand-button {
          background: none;
          border: none;
          color: #64748b;
          cursor: pointer;
        }

        .flag-details {
          padding: 1.5rem;
          background-color: #f8fafc;
          border-top: 1px solid #e5e7eb;
        }

        .section-title {
          display: flex;
          align-items: center;
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1rem;
        }

        .media-section {
          margin-bottom: 1.5rem;
        }

        .media-grid {
          display: grid;
          grid-template-columns: 1fr 2fr;
          gap: 1rem;
        }

        .video-preview {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .video-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 120px;
          background-color: #e5e7eb;
          color: #9ca3af;
          border-radius: 0.5rem;
        }

        .play-button {
          padding: 0.5rem;
          background-color: #15803d;
          color: white;
          border: none;
          border-radius: 0.25rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .play-button:hover {
          background-color: #166534;
        }

        .screenshots {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
          gap: 0.5rem;
        }

        .screenshot-thumbnail {
          position: relative;
          border-radius: 0.25rem;
          overflow: hidden;
        }

        .screenshot-thumbnail img {
          width: 100%;
          height: 80px;
          object-fit: cover;
        }

        .download-button {
          position: absolute;
          top: 0.25rem;
          right: 0.25rem;
          width: 24px;
          height: 24px;
          background-color: rgba(255, 255, 255, 0.8);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
          cursor: pointer;
        }

        .annotation-section {
          margin-bottom: 1.5rem;
        }

        .existing-notes {
          background-color: white;
          border: 1px solid #d1d5db;
          border-radius: 0.5rem;
          padding: 1rem;
          position: relative;
        }
        .existing-notes p {
          margin: 0;
          color: #374151;
        }
        .edit-notes-button {
          position: absolute;
          top: 0.5rem;
          right: 0.5rem;
          background-color: #e5e7eb;
          color: #374151;
          border: none;
          border-radius: 0.25rem;
          padding: 0.25rem 0.5rem;
          font-size: 0.875rem;
          cursor: pointer;
        }
        .edit-notes-button:hover {
          background-color: #d1d5db;
        }
        .add-notes textarea {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.5rem;
          font-family: inherit;
          resize: vertical;
          min-height: 80px;
        }

        .save-notes-button {
          margin-top: 0.5rem;
          padding: 0.5rem 1rem;
          background-color: #15803d;
          color: white;
          border: none;
          border-radius: 0.25rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .save-notes-button:hover {
          background-color: #166534;
        }

        .save-notes-button:disabled {
          background-color: #d1d5db;
          cursor: not-allowed;
        }

        .flag-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 1rem;
          border-top: 1px solid #e5e7eb;
        }

        .status-actions {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .action-label {
          font-size: 0.875rem;
          color: #64748b;
        }

        .status-button {
          display: flex;
          align-items: center;
          padding: 0.5rem 1rem;
          border-radius: 0.25rem;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          border: 1px solid #d1d5db;
          background-color: white;
        }

        .status-button:hover {
          background-color: #f3f4f6;
        }

        .status-button.active {
          background-color: #f0fdf4;
          border-color: #86efac;
        }

        .status-button.reviewed.active {
          color: #166534;
        }

        .status-button.dismissed.active {
          color: #991b1b;
          background-color: #fee2e2;
          border-color: #fecaca;
        }

        .utility-actions {
          display: flex;
          gap: 0.5rem;
        }

        .utility-button {
          display: flex;
          align-items: center;
          padding: 0.5rem 1rem;
          border-radius: 0.25rem;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          border: 1px solid #d1d5db;
          background-color: white;
        }

        .utility-button:hover {
          background-color: #f3f4f6;
        }
      `}</style>
    </div>
  );
};

export default FlagReview;