import React, { useState } from 'react';
import { 
  HelpCircle,
  Mail,
  MessageSquare,
  Phone,
  Download,
  PlayCircle,
  ChevronDown,
  ChevronUp,
  CheckCircle,
  AlertCircle,
  FileText,
  Video
} from 'lucide-react';

const Help = () => {
  const [activeTab, setActiveTab] = useState('faq');
  const [activeQuestion, setActiveQuestion] = useState<number | null>(null);

  const faqs = [
    {
      question: 'How do I reset my password?',
      answer: 'You can reset your password by clicking on the "Forgot Password" link on the login page. You will receive an email with instructions to reset your password.'
    },
    {
      question: 'What should I do if my exam gets interrupted?',
      answer: 'If your exam gets interrupted due to technical issues, immediately contact support. Your progress may be saved depending on when the interruption occurred.'
    },
    {
      question: 'How are exams proctored?',
      answer: 'Exams are proctored using AI monitoring that tracks your webcam, microphone, and screen activity. Any suspicious behavior may be flagged for review.'
    },
    {
      question: 'Can I retake an exam if I fail?',
      answer: 'Retake policies vary by course. Please check with your instructor about the specific retake policy for your exam.'
    }
  ];

  const contactMethods = [
    {
      name: 'Email Support',
      description: 'Get help via email with response within 24 hours',
      contact: '<EMAIL>',
      icon: <Mail size={20} />
    },
    {
      name: 'Live Chat',
      description: 'Chat with a support agent in real-time',
      contact: 'Available 9AM-5PM (Mon-Fri)',
      icon: <MessageSquare size={20} />
    },
    {
      name: 'Phone Support',
      description: 'Call us for immediate assistance',
      contact: '+****************',
      icon: <Phone size={20} />
    }
  ];

  const toggleQuestion = (index: number) => {
    setActiveQuestion(activeQuestion === index ? null : index);
  };

  return (
    <div className="help-page">
      <div className="help-content">
        <h1 className="help-title">
          <HelpCircle size={28} className="title-icon" />
          Help Center
        </h1>
        
        <div className="help-card">
          <div className="tab-container">
            <nav className="tab-nav">
              <button
                onClick={() => setActiveTab('faq')}
                className={`tab-button ${activeTab === 'faq' ? 'active' : ''}`}
              >
                FAQs
              </button>
              <button
                onClick={() => setActiveTab('contact')}
                className={`tab-button ${activeTab === 'contact' ? 'active' : ''}`}
              >
                Contact Support
              </button>
              <button
                onClick={() => setActiveTab('resources')}
                className={`tab-button ${activeTab === 'resources' ? 'active' : ''}`}
              >
                Resources
              </button>
            </nav>
          </div>
          
          <div className="tab-content">
            {activeTab === 'faq' && (
              <div className="faq-section">
                <h2 className="section-title">Frequently Asked Questions</h2>
                <div className="faq-list">
                  {faqs.map((faq, index) => (
                    <div key={index} className="faq-item">
                      <button
                        onClick={() => toggleQuestion(index)}
                        className="faq-question"
                      >
                        <span>{faq.question}</span>
                        {activeQuestion === index ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
                      </button>
                      {activeQuestion === index && (
                        <div className="faq-answer">
                          {faq.answer}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {activeTab === 'contact' && (
              <div className="contact-section">
                <h2 className="section-title">Contact Our Support Team</h2>
                <div className="contact-methods">
                  {contactMethods.map((method, index) => (
                    <div key={index} className="contact-card">
                      <div className="contact-icon">
                        {method.icon}
                      </div>
                      <div className="contact-details">
                        <h3 className="contact-name">{method.name}</h3>
                        <p className="contact-description">{method.description}</p>
                        <p className="contact-info">{method.contact}</p>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="message-form">
                  <h3 className="form-title">Send us a message</h3>
                  <form className="form-fields">
                    <div className="form-group">
                      <label htmlFor="subject" className="form-label">Subject</label>
                      <input
                        type="text"
                        id="subject"
                        className="form-input"
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="message" className="form-label">Message</label>
                      <textarea
                        id="message"
                        rows={4}
                        className="form-input"
                      ></textarea>
                    </div>
                    <button
                      type="submit"
                      className="submit-button"
                    >
                      Send Message
                    </button>
                  </form>
                </div>
              </div>
            )}
            
            {activeTab === 'resources' && (
              <div className="resources-section">
                <h2 className="section-title">Helpful Resources</h2>
                <div className="resources-list">
                  <div className="resource-card">
                    <div className="resource-icon">
                      <FileText size={20} />
                    </div>
                    <div className="resource-content">
                      <h3 className="resource-name">Exam Preparation Guide</h3>
                      <p className="resource-description">
                        Download our comprehensive guide to preparing for online exams, including technical setup, study tips, and exam day strategies.
                      </p>
                      <button className="resource-action">
                        <Download size={16} className="action-icon" />
                        Download PDF
                      </button>
                    </div>
                  </div>
                  
                  <div className="resource-card">
                    <div className="resource-icon">
                      <Video size={20} />
                    </div>
                    <div className="resource-content">
                      <h3 className="resource-name">Video Tutorials</h3>
                      <p className="resource-description">
                        Watch our video tutorials that walk you through the exam platform features, navigation, and troubleshooting common issues.
                      </p>
                      <div className="video-links">
                        <a href="#" className="video-link">
                          <PlayCircle size={16} className="link-icon" />
                          Platform Overview
                        </a>
                        <a href="#" className="video-link">
                          <PlayCircle size={16} className="link-icon" />
                          Taking Exams
                        </a>
                        <a href="#" className="video-link">
                          <PlayCircle size={16} className="link-icon" />
                          Troubleshooting
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className="status-card">
          <h2 className="status-title">System Status</h2>
          <div className="status-indicator">
            <CheckCircle size={20} className="status-icon" />
            <span className="status-text">All systems operational</span>
          </div>
          <p className="status-update">Last updated: {new Date().toLocaleString()}</p>
        </div>
      </div>

      <style>{`
        .help-page {
          display: flex;
          min-height: 100vh;
          background-color: #f8fafc;
        }

        .help-content {
          flex: 1;
          padding: 2rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .help-title {
          display: flex;
          align-items: center;
          font-size: 2rem;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 2rem;
          gap: 0.75rem;
        }

        .title-icon {
          color: #4f46e5;
        }

        .help-card {
          background-color: white;
          border-radius: 0.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          margin-bottom: 1.5rem;
          overflow: hidden;
        }

        .tab-container {
          border-bottom: 1px solid #e2e8f0;
        }

        .tab-nav {
          display: flex;
        }

        .tab-button {
          padding: 1rem 1.5rem;
          font-weight: 500;
          color: #64748b;
          border-bottom: 2px solid transparent;
          transition: all 0.2s;
        }

        .tab-button:hover {
          color: #4f46e5;
          border-bottom-color: #c7d2fe;
        }

        .tab-button.active {
          color: #4f46e5;
          border-bottom-color: #4f46e5;
        }

        .tab-content {
          padding: 1.5rem;
        }

        .section-title {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 1.5rem;
        }

        .faq-list {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
        }

        .faq-item {
          border: 1px solid #e2e8f0;
          border-radius: 0.5rem;
          overflow: hidden;
        }

        .faq-question {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          padding: 1rem;
          background-color: white;
          border: none;
          font-weight: 500;
          color: #1e293b;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .faq-question:hover {
          background-color: #f8fafc;
        }

        .faq-answer {
          padding: 1rem;
          background-color: #f8fafc;
          border-top: 1px solid #e2e8f0;
          color: #475569;
        }

        .contact-methods {
          display: grid;
          grid-template-columns: repeat(1, 1fr);
          gap: 1rem;
          margin-bottom: 2rem;
        }

        @media (min-width: 768px) {
          .contact-methods {
            grid-template-columns: repeat(3, 1fr);
          }
        }

        .contact-card {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          padding: 1.5rem;
          border: 1px solid #e2e8f0;
          border-radius: 0.5rem;
          transition: all 0.2s;
        }

        .contact-card:hover {
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .contact-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 3rem;
          height: 3rem;
          background-color: #e0e7ff;
          color: #4f46e5;
          border-radius: 9999px;
          margin-bottom: 1rem;
        }

        .contact-name {
          font-size: 1rem;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 0.5rem;
        }

        .contact-description {
          font-size: 0.875rem;
          color: #64748b;
          margin-bottom: 0.5rem;
        }

        .contact-info {
          font-size: 0.875rem;
          font-weight: 500;
          color: #4f46e5;
        }

        .message-form {
          margin-top: 2rem;
        }

        .form-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 1rem;
        }

        .form-fields {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .form-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .form-label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #374151;
        }

        .form-input {
          width: 100%;
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .form-input:focus {
          outline: none;
          border-color: #818cf8;
          box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
        }

        textarea.form-input {
          min-height: 6rem;
        }

        .submit-button {
          align-self: flex-start;
          padding: 0.5rem 1.5rem;
          background-color: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .submit-button:hover {
          background-color: #4338ca;
        }

        .resources-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .resource-card {
          display: flex;
          gap: 1.5rem;
          padding: 1.5rem;
          border: 1px solid #e2e8f0;
          border-radius: 0.5rem;
          transition: all 0.2s;
        }

        .resource-card:hover {
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .resource-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 3rem;
          height: 3rem;
          background-color: #e0e7ff;
          color: #4f46e5;
          border-radius: 0.5rem;
          flex-shrink: 0;
        }

        .resource-content {
          flex: 1;
        }

        .resource-name {
          font-size: 1rem;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 0.5rem;
        }

        .resource-description {
          font-size: 0.875rem;
          color: #64748b;
          margin-bottom: 1rem;
        }

        .resource-action {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: #4f46e5;
          font-weight: 500;
          background: none;
          border: none;
          cursor: pointer;
          padding: 0;
        }

        .action-icon {
          margin-right: 0.25rem;
        }

        .video-links {
          display: grid;
          grid-template-columns: repeat(1, 1fr);
          gap: 0.75rem;
        }

        @media (min-width: 640px) {
          .video-links {
            grid-template-columns: repeat(3, 1fr);
          }
        }

        .video-link {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          color: #4f46e5;
          font-size: 0.875rem;
          font-weight: 500;
        }

        .link-icon {
          margin-right: 0.25rem;
        }

        .status-card {
          background-color: white;
          border-radius: 0.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          padding: 1.5rem;
        }

        .status-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 1rem;
        }

        .status-indicator {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          margin-bottom: 0.5rem;
        }

        .status-icon {
          color: #10b981;
        }

        .status-text {
          font-weight: 500;
          color: #1e293b;
        }

        .status-update {
          font-size: 0.875rem;
          color: #64748b;
        }
      `}</style>
    </div>
  );
};

export default Help;