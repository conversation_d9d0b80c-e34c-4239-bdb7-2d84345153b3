import { useState, useEffect } from 'react';
import { FiUsers, FiActivity, FiAlertTriangle, FiClock, FiServer, FiBarChart2, <PERSON>Shield, FiList, FiTrendingUp } from 'react-icons/fi';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const Dashboard = () => {
  const [stats, setStats] = useState({
    users: 0,
    exams: 0,
    flags: 0,
    uptime: '0%',
    activeUsers: 0,
    systemHealth: 'Loading...',
    storageUsed: '0GB/0GB',
    responseTime: '0ms'
  });

  type AuditLog = {
    id: number;
    action: string;
    user: string;
    timestamp: string;
    status: string;
  };
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');

  // Simulate data fetching
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Mock data based on selected time range (for demonstration)
      const mockStats = {
        '24h': { users: 1300, exams: 60, flags: 5, uptime: '99.99%', activeUsers: 400, systemHealth: 'Excellent', storageUsed: '25GB/100GB', responseTime: '35ms' },
        '7d': { users: 1243, exams: 58, flags: 14, uptime: '99.98%', activeUsers: 327, systemHealth: 'Good', storageUsed: '24GB/100GB', responseTime: '42ms' },
        '30d': { users: 1100, exams: 200, flags: 25, uptime: '99.95%', activeUsers: 250, systemHealth: 'Stable', storageUsed: '23GB/100GB', responseTime: '55ms' },
      };

      const mockAuditLogs = {
        '24h': [
          { id: 1, action: 'User login', user: '<EMAIL>', timestamp: '2025-05-23T10:32:45Z', status: 'success' },
          { id: 2, action: 'Exam created', user: '<EMAIL>', timestamp: '2025-05-23T09:45:12Z', status: 'success' },
          { id: 3, action: 'Failed login attempt', user: '<EMAIL>', timestamp: '2025-05-23T08:18:33Z', status: 'failed' },
        ],
        '7d': [
          { id: 1, action: 'User login', user: '<EMAIL>', timestamp: '2025-05-22T14:32:45Z', status: 'success' },
          { id: 2, action: 'Exam created', user: '<EMAIL>', timestamp: '2025-05-21T13:45:12Z', status: 'success' },
          { id: 3, action: 'Failed login attempt', user: '<EMAIL>', timestamp: '2025-05-20T12:18:33Z', status: 'failed' },
          { id: 4, action: 'User registered', user: '<EMAIL>', timestamp: '2025-05-19T10:05:21Z', status: 'success' },
          { id: 5, action: 'System backup', user: 'system', timestamp: '2025-05-18T04:00:00Z', status: 'success' },
        ],
        '30d': [
          { id: 1, action: 'User login', user: '<EMAIL>', timestamp: '2025-05-15T14:32:45Z', status: 'success' },
          { id: 2, action: 'Exam created', user: '<EMAIL>', timestamp: '2025-05-14T13:45:12Z', status: 'success' },
          { id: 3, action: 'Failed login attempt', user: '<EMAIL>', timestamp: '2025-05-13T12:18:33Z', status: 'failed' },
          { id: 4, action: 'User registered', user: '<EMAIL>', timestamp: '2025-05-12T10:05:21Z', status: 'success' },
          { id: 5, action: 'System backup', user: 'system', timestamp: '2025-05-11T04:00:00Z', status: 'success' },
          { id: 6, action: 'User password reset', user: '<EMAIL>', timestamp: '2025-05-10T11:00:00Z', status: 'success' },
          { id: 7, action: 'Exam deleted', user: '<EMAIL>', timestamp: '2025-05-09T16:00:00Z', status: 'success' },
        ],
      }
      
      setStats(mockStats[timeRange as keyof typeof mockStats]);
      setAuditLogs(mockAuditLogs[timeRange as keyof typeof mockAuditLogs]);

      setLoading(false);
    };

    fetchData();
  }, [timeRange]); // Re-fetch data when timeRange changes

  // Chart data
  const userGrowthData = {
    labels: timeRange === '24h' ? ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'] :
              timeRange === '7d' ? ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'] :
              ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6'],
    datasets: [
      {
        label: 'New Users',
        data: timeRange === '24h' ? [10, 15, 25, 30, 40, 35, 20] :
              timeRange === '7d' ? [50, 70, 60, 90, 80, 100, 120] :
              [200, 250, 220, 280, 350, 400],
        borderColor: '#4f46e5', // Primary color
        backgroundColor: 'rgba(79, 70, 229, 0.1)', // Light primary color
        tension: 0.4, // Smoother line
        fill: true,
        pointBackgroundColor: '#4f46e5',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 5,
        hoverPointRadius: 7,
      }
    ]
  };

  const systemUsageData = {
    labels: ['CPU', 'Memory', 'Storage', 'Network'],
    datasets: [
      {
        label: 'Usage %',
        data: [stats.users / 1000 * 40, stats.activeUsers / 500 * 60, parseFloat(stats.storageUsed.split('/')[0]) / parseFloat(stats.storageUsed.split('/')[1]) * 100, parseFloat(stats.responseTime) / 100 * 50], // Dynamic usage
        backgroundColor: [
          'rgba(79, 70, 229, 0.8)', // Primary
          'rgba(16, 185, 129, 0.8)', // Green
          'rgba(245, 158, 11, 0.8)',  // Amber
          'rgba(59, 130, 246, 0.8)'   // Blue
        ],
        borderColor: [
          '#4f46e5',
          '#10b981',
          '#f59e0b',
          '#3b82f6'
        ],
        borderWidth: 1
      }
    ]
  };

  interface StatusColorMap {
    [key: string]: string;
  }

  const getStatusColor = (status: string): string => {
      switch (status.toLowerCase()) {
          case 'success': return 'status-success';
          case 'failed': return 'status-failed';
          default: return 'status-default';
      }
  };

  return (
    <div className="dashboard-page-container">
      <div className="dashboard-wrapper">
        <div className="dashboard-header">
          <h1 className="dashboard-title">System Overview Dashboard</h1>
          <div className="time-range-selector">
            <button 
              onClick={() => setTimeRange('24h')} 
              className={timeRange === '24h' ? 'active' : ''}
            >
              24H
            </button>
            <button 
              onClick={() => setTimeRange('7d')} 
              className={timeRange === '7d' ? 'active' : ''}
            >
              7D
            </button>
            <button 
              onClick={() => setTimeRange('30d')} 
              className={timeRange === '30d' ? 'active' : ''}
            >
              30D
            </button>
          </div>
        </div>

        {loading ? (
          <div className="loading-state">
            <div className="spinner"></div>
            <p>Loading dashboard data...</p>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-card-header">
                  <div className="stat-icon-wrapper primary">
                    <FiUsers size={24} />
                  </div>
                  <div className="stat-info">
                    <p className="stat-label">Total Users</p>
                    <p className="stat-value">{stats.users}</p>
                  </div>
                </div>
                <div className="stat-footer">
                  <FiTrendingUp className="trend-icon success" />
                  <span className="trend-text">12.5% increase</span>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-card-header">
                  <div className="stat-icon-wrapper green">
                    <FiActivity size={24} />
                  </div>
                  <div className="stat-info">
                    <p className="stat-label">Active Users</p>
                    <p className="stat-value">{stats.activeUsers}</p>
                  </div>
                </div>
                <div className="stat-footer">
                  <FiTrendingUp className="trend-icon success" />
                  <span className="trend-text">8.3% increase</span>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-card-header">
                  <div className="stat-icon-wrapper red">
                    <FiAlertTriangle size={24} />
                  </div>
                  <div className="stat-info">
                    <p className="stat-label">Flags / Alerts</p>
                    <p className="stat-value">{stats.flags}</p>
                  </div>
                </div>
                <div className="stat-footer">
                  <FiTrendingUp className="trend-icon danger" />
                  <span className="trend-text">3 new today</span>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-card-header">
                  <div className="stat-icon-wrapper blue">
                    <FiServer size={24} />
                  </div>
                  <div className="stat-info">
                    <p className="stat-label">System Health</p>
                    <p className="stat-value">{stats.systemHealth}</p>
                  </div>
                </div>
                <div className="stat-footer">
                  <FiClock className="trend-icon default" />
                  <span className="trend-text">Uptime: {stats.uptime}</span>
                </div>
              </div>
            </div>

            {/* Charts Row */}
            <div className="charts-grid">
              <div className="chart-card">
                <div className="chart-header">
                  <h2>User Growth</h2>
                  <p className="chart-subtitle">New registrations over time</p>
                </div>
                <div className="chart-container">
                  <Line 
                    data={userGrowthData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'top',
                          labels: {
                            font: {
                              family: 'Inter',
                              size: 14,
                            },
                            color: '#4b5563',
                          }
                        },
                        tooltip: {
                          titleFont: { family: 'Inter', size: 14, weight: 'bold' },
                          bodyFont: { family: 'Inter', size: 12 },
                          backgroundColor: 'rgba(0,0,0,0.7)',
                          padding: 10,
                          cornerRadius: 6,
                        }
                      },
                      scales: {
                        x: {
                          ticks: { color: '#6b7280', font: { family: 'Inter' } },
                          grid: { color: 'rgba(0,0,0,0.05)' }
                        },
                        y: {
                          beginAtZero: true,
                          ticks: { color: '#6b7280', font: { family: 'Inter' } },
                          grid: { color: 'rgba(0,0,0,0.05)' }
                        }
                      }
                    }}
                  />
                </div>
              </div>

              <div className="chart-card">
                <div className="chart-header">
                  <h2>System Resource Usage</h2>
                  <p className="chart-subtitle">Current allocation of key resources</p>
                </div>
                <div className="chart-container">
                  <Bar
                    data={systemUsageData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'top',
                          labels: {
                            font: {
                              family: 'Inter',
                              size: 14,
                            },
                            color: '#4b5563',
                          }
                        },
                        tooltip: {
                          titleFont: { family: 'Inter', size: 14, weight: 'bold' },
                          bodyFont: { family: 'Inter', size: 12 },
                          backgroundColor: 'rgba(0,0,0,0.7)',
                          padding: 10,
                          cornerRadius: 6,
                        }
                      },
                      scales: {
                        x: {
                          ticks: { color: '#6b7280', font: { family: 'Inter' } },
                          grid: { color: 'rgba(0,0,0,0.05)' }
                        },
                        y: {
                          beginAtZero: true,
                          max: 100,
                          ticks: { color: '#6b7280', font: { family: 'Inter' } },
                          grid: { color: 'rgba(0,0,0,0.05)' }
                        }
                      }
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Bottom Row */}
            <div className="bottom-grid">
              <div className="activity-card">
                <div className="activity-header">
                  <h2>Recent Audit Logs</h2>
                  <button className="view-all-button">
                    <FiList size={18} /> View All
                  </button>
                </div>
                <div className="activity-table-wrapper">
                  <table>
                    <thead>
                      <tr>
                        <th>Action</th>
                        <th>User</th>
                        <th>Time</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {auditLogs.map((log) => (
                        <tr key={log.id}>
                          <td>{log.action}</td>
                          <td>{log.user}</td>
                          <td>{new Date(log.timestamp).toLocaleString()}</td>
                          <td>
                            <span className={`status-badge ${getStatusColor(log.status)}`}>
                              {log.status}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              <div className="quick-stats-card">
                <div className="quick-stats-header">
                  <h2>Quick Metrics</h2>
                  <FiBarChart2 size={20} className="header-icon" />
                </div>
                <div className="quick-stats-content">
                  <div className="quick-stat-item">
                    <p className="stat-label">Storage Used</p>
                    <p className="stat-value">{stats.storageUsed}</p>
                    <div className="progress-bar-wrapper">
                      <div className="progress-bar-inner blue" style={{ width: `${(parseFloat(stats.storageUsed.split('/')[0]) / parseFloat(stats.storageUsed.split('/')[1]) * 100).toFixed(0)}%` }}></div>
                    </div>
                  </div>
                  <div className="quick-stat-item">
                    <p className="stat-label">Avg. Response Time</p>
                    <p className="stat-value">{stats.responseTime}</p>
                    <div className="progress-bar-wrapper">
                      <div className="progress-bar-inner primary" style={{ width: `${(100 - parseFloat(stats.responseTime) / 100 * 100).toFixed(0)}%` }}></div> {/* Inverse for green if lower is better */}
                    </div>
                  </div>
                  <div className="quick-stat-item">
                    <p className="stat-label">Exams Created</p>
                    <p className="stat-value">{stats.exams}</p>
                    <div className="progress-bar-wrapper">
                      <div className="progress-bar-inner green" style={{ width: `${(stats.exams / 100 * 100).toFixed(0)}%` }}></div>
                    </div>
                  </div>
                  <div className="report-button-area">
                    <button className="generate-report-button">
                      <FiShield size={18} />
                      Generate System Report
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      <style>{`
        /* Global Variables (consistent with other components) */
        :root {
            --primary-color: #4f46e5;      /* Indigo-600 */
            --primary-dark: #4338ca;       /* Indigo-700 */
            --primary-light: #eef2ff;      /* Indigo-50 */
            --green-success: #10b981;      /* Emerald-500 */
            --green-light: #ecfdf5;        /* Emerald-50 */
            --red-danger: #ef4444;         /* Red-500 */
            --red-light: #fee2e2;          /* Red-50 */
            --yellow-warning: #f59e0b;     /* Amber-500 */
            --yellow-light: #fffbeb;       /* Amber-50 */
            --blue-info: #3b82f6;          /* Blue-500 */
            --blue-light: #eff6ff;         /* Blue-50 */
            --purple-accent: #8b5cf6;      /* Purple-500 */
            --purple-light: #f5f3ff;       /* Purple-50 */
            
            --text-dark: #1f2937;          /* Gray-900 */
            --text-medium: #4b5563;        /* Gray-700 */
            --text-light: #6b7280;         /* Gray-500 */
            --border-color: #e5e7eb;       /* Gray-200 */
            --bg-light: #f9fafb;           /* Gray-50 */
            --bg-gradient-start: #f0f4ff;
            --bg-gradient-end: #e6f0ff;
            
            --card-bg: white;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
        }

        .dashboard-page-container {
          min-height: 100vh;
          background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
          padding: 2.5rem 2rem;
          font-family: 'Inter', sans-serif;
          color: var(--text-dark);
        }

        .dashboard-wrapper {
          max-width: 1280px;
          margin: 0 auto;
        }

        /* Header */
        .dashboard-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2.5rem;
          background: var(--card-bg);
          padding: 1.5rem 2rem;
          border-radius: 0.75rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
        }

        .dashboard-title {
          font-size: 2rem;
          font-weight: 800;
          color: var(--primary-dark);
          letter-spacing: -0.03em;
        }

        .time-range-selector {
          display: flex;
          background: var(--bg-light);
          border-radius: 0.5rem;
          padding: 0.25rem;
          border: 1px solid var(--border-color);
        }

        .time-range-selector button {
          padding: 0.6rem 1.2rem;
          border-radius: 0.375rem;
          border: none;
          background: transparent;
          color: var(--text-medium);
          font-size: 0.9rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease-in-out;
        }

        .time-range-selector button:hover {
          color: var(--primary-color);
        }

        .time-range-selector button.active {
          background: var(--primary-color);
          color: white;
          box-shadow: var(--shadow-sm);
        }

        /* Loading State */
        .loading-state {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          height: 400px;
          background: var(--card-bg);
          border-radius: 1rem;
          box-shadow: var(--shadow-md);
          border: 1px solid var(--border-color);
          color: var(--text-medium);
          font-size: 1.1rem;
        }

        .spinner {
          width: 3.5rem;
          height: 3.5rem;
          border: 5px solid var(--primary-light);
          border-top-color: var(--primary-color);
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 1rem;
        }

        @keyframes spin {
          to { transform: rotate(360deg); }
        }

        /* Stats Grid */
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 1.5rem;
          margin-bottom: 2.5rem;
        }

        .stat-card {
          background: var(--card-bg);
          border-radius: 0.75rem;
          padding: 1.8rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
          transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .stat-card:hover {
          transform: translateY(-5px);
          box-shadow: var(--shadow-md);
        }

        .stat-card-header {
          display: flex;
          align-items: center;
          gap: 1rem;
          margin-bottom: 1.2rem;
        }

        .stat-icon-wrapper {
          display: inline-flex;
          padding: 0.9rem;
          border-radius: 50%;
          box-shadow: var(--shadow-sm);
        }

        .stat-icon-wrapper.primary { background: var(--primary-light); color: var(--primary-color); }
        .stat-icon-wrapper.green { background: var(--green-light); color: var(--green-success); }
        .stat-icon-wrapper.red { background: var(--red-light); color: var(--red-danger); }
        .stat-icon-wrapper.blue { background: var(--blue-light); color: var(--blue-info); }
        
        .stat-info {
            flex-grow: 1;
        }

        .stat-label {
          font-size: 0.9rem;
          color: var(--text-light);
          margin: 0;
          font-weight: 500;
        }

        .stat-value {
          font-size: 1.8rem;
          font-weight: 700;
          color: var(--text-dark);
          margin: 0.25rem 0 0 0;
          letter-spacing: -0.02em;
        }

        .stat-footer {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding-top: 1rem;
          border-top: 1px solid var(--border-color);
          font-size: 0.85rem;
          color: var(--text-medium);
        }

        .trend-icon {
          width: 1.1rem;
          height: 1.1rem;
        }
        .trend-icon.success { color: var(--green-success); }
        .trend-icon.danger { color: var(--red-danger); }
        .trend-icon.default { color: var(--text-light); }

        .trend-text {
            font-weight: 600;
            color: var(--text-dark);
        }

        /* Charts Grid */
        .charts-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
          gap: 1.5rem;
          margin-bottom: 2.5rem;
        }

        .chart-card {
          background: var(--card-bg);
          border-radius: 0.75rem;
          padding: 1.8rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
        }

        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
        }

        .chart-header h2 {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--text-dark);
          margin: 0;
        }

        .chart-subtitle {
          font-size: 0.9rem;
          color: var(--text-light);
          margin: 0;
        }

        .chart-container {
          height: 320px; /* Increased height for better chart visibility */
        }

        /* Bottom Grid (Activity and Quick Stats) */
        .bottom-grid {
          display: grid;
          grid-template-columns: 2.5fr 1fr; /* Adjusted ratio */
          gap: 1.5rem;
        }

        .activity-card, .quick-stats-card {
          background: var(--card-bg);
          border-radius: 0.75rem;
          padding: 1.8rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
        }

        .activity-header, .quick-stats-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
        }

        .activity-header h2, .quick-stats-header h2 {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--text-dark);
          margin: 0;
        }

        .quick-stats-header .header-icon {
            color: var(--text-light);
        }

        .view-all-button {
          background: transparent;
          border: none;
          color: var(--primary-color);
          font-size: 0.9rem;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 0.4rem;
          padding: 0.5rem 0.75rem;
          border-radius: 0.375rem;
          transition: background 0.2s, color 0.2s;
        }

        .view-all-button:hover {
          background: var(--primary-light);
          color: var(--primary-dark);
        }

        .activity-table-wrapper {
          overflow-x: auto;
          -webkit-overflow-scrolling: touch; /* for smoother scrolling on iOS */
        }

        table {
          width: 100%;
          border-collapse: collapse;
          min-width: 600px; /* Ensure table doesn't get too small */
        }

        th {
          text-align: left;
          padding: 0.9rem 1.2rem;
          font-size: 0.8rem;
          font-weight: 600;
          color: var(--text-light);
          text-transform: uppercase;
          background: var(--bg-light);
          border-bottom: 1px solid var(--border-color);
        }

        td {
          padding: 1rem 1.2rem;
          font-size: 0.9rem;
          color: var(--text-medium);
          border-top: 1px solid var(--border-color);
        }

        tbody tr:last-child td {
            border-bottom: none;
        }

        tbody tr:hover {
            background-color: var(--bg-light);
        }

        .status-badge {
          display: inline-block;
          padding: 0.3rem 0.7rem;
          border-radius: 0.5rem;
          font-size: 0.75rem;
          font-weight: 700;
          text-transform: capitalize;
        }

        .status-success {
          background: var(--green-light);
          color: var(--green-success);
        }

        .status-failed {
          background: var(--red-light);
          color: var(--red-danger);
        }

        .status-default {
          background: var(--bg-light);
          color: var(--text-medium);
        }

        /* Quick Stats */
        .quick-stats-content {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }

        .quick-stat-item {
          padding-bottom: 1rem;
          border-bottom: 1px dashed var(--border-color);
        }
        .quick-stat-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        .quick-stat-item .stat-label {
          font-size: 0.9rem;
          color: var(--text-light);
          margin: 0 0 0.25rem 0;
        }

        .quick-stat-item .stat-value {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--text-dark);
          margin: 0 0 0.5rem 0;
        }

        .progress-bar-wrapper {
            height: 0.6rem;
            background: var(--bg-light);
            border-radius: 0.3rem;
            overflow: hidden;
        }
        .progress-bar-inner {
            height: 100%;
            border-radius: 0.3rem;
            transition: width 0.5s ease-out;
        }
        .progress-bar-inner.primary { background: var(--primary-color); }
        .progress-bar-inner.green { background: var(--green-success); }
        .progress-bar-inner.red { background: var(--red-danger); }
        .progress-bar-inner.blue { background: var(--blue-info); }

        .report-button-area {
          padding-top: 1.5rem;
          margin-top: 0.5rem;
          border-top: 1px solid var(--border-color);
        }

        .generate-report-button {
          width: 100%;
          padding: 0.9rem;
          background: var(--primary-color);
          color: white;
          border: none;
          border-radius: 0.6rem;
          font-weight: 600;
          font-size: 1rem;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.6rem;
          transition: all 0.3s ease-in-out;
          box-shadow: 0 4px 10px rgba(79, 70, 229, 0.2);
        }

        .generate-report-button:hover {
          background: var(--primary-dark);
          box-shadow: 0 6px 15px rgba(79, 70, 229, 0.3);
          transform: translateY(-1px);
        }

        /* Responsive adjustments */
        @media (max-width: 1200px) {
          .charts-grid {
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
          }
        }

        @media (max-width: 1024px) {
          .dashboard-page-container {
            padding: 2rem 1.5rem;
          }
          .dashboard-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.2rem 1.5rem;
          }
          .dashboard-title {
            font-size: 1.75rem;
          }
          .stats-grid, .charts-grid, .bottom-grid {
            gap: 1rem;
          }
          .charts-grid {
             grid-template-columns: 1fr; /* Stack charts on medium screens */
          }
          .bottom-grid {
            grid-template-columns: 1fr; /* Stack bottom cards on medium screens */
          }
        }

        @media (max-width: 768px) {
          .dashboard-page-container {
            padding: 1.5rem 1rem;
          }
          .dashboard-wrapper {
            padding: 0;
          }
          .dashboard-header {
            padding: 1rem;
          }
          .dashboard-title {
            font-size: 1.5rem;
          }
          .stats-grid {
            grid-template-columns: 1fr; /* Stack stats cards on small screens */
          }
          .stat-card {
            padding: 1.2rem;
          }
          .stat-icon-wrapper {
            padding: 0.7rem;
          }
          .stat-value {
            font-size: 1.6rem;
          }
          .chart-card, .activity-card, .quick-stats-card {
            padding: 1.2rem;
          }
          table {
            min-width: 500px; /* Adjust min-width for table on smaller screens */
          }
          th, td {
            padding: 0.8rem 1rem;
          }
        }
      `}</style>
    </div>
  );
};

export default Dashboard;