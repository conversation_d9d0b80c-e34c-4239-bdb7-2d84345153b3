import React, { useState } from 'react';
import { 
  Setting<PERSON>,
  Clock,
  BookOpen,
  List,
  Plus,
  Trash2,
  Save,
  ChevronDown,
  ChevronUp,
  AlertCircle,
  Eye,
  EyeOff,
  Calendar,
  Users
} from 'lucide-react';

type Question = {
  id: number;
  text: string;
  type: 'multiple-choice' | 'short-answer' | 'essay';
  points: number;
  options: string[];
  correctAnswer: string | number;
};

type ExamData = {
  title: string;
  description: string;
  duration: number;
  isTimed: boolean;
  showResults: boolean;
  allowRetakes: boolean;
  startDate: string;
  endDate: string;
  passingScore: number;
  proctoring: {
    webcam: boolean;
    microphone: boolean;
    screenCapture: boolean;
  };
  questions: Question[];
  allowedStudents: string[];
};

const CreateNewExam = () => {
  const [examData, setExamData] = useState<ExamData>({
    title: '',
    description: '',
    duration: 60,
    isTimed: true,
    showResults: false,
    allowRetakes: false,
    startDate: '',
    endDate: '',
    passingScore: 70,
    proctoring: {
      webcam: false,
      microphone: false,
      screenCapture: false
    },
    questions: [],
    allowedStudents: []
  });

  const [activeTab, setActiveTab] = useState('settings');
  const [newQuestionType, setNewQuestionType] = useState('multiple-choice');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showStudentList, setShowStudentList] = useState(false);
  const [newStudentEmail, setNewStudentEmail] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setExamData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleProctoringChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setExamData(prev => ({
      ...prev,
      proctoring: {
        ...prev.proctoring,
        [name]: checked
      }
    }));
  };

  const addQuestion = () => {
    const newQuestion: Question = {
      id: examData.questions.length + 1,
      text: '',
      type: newQuestionType as 'multiple-choice' | 'short-answer' | 'essay',
      points: 1,
      options: newQuestionType === 'multiple-choice' ? ['', '', '', ''] : [],
      correctAnswer: newQuestionType === 'multiple-choice' ? 0 : ''
    };
    setExamData(prev => ({
      ...prev,
      questions: [...prev.questions, newQuestion]
    }));
  };

  const removeQuestion = (id: number) => {
    setExamData(prev => ({
      ...prev,
      questions: prev.questions.filter(q => q.id !== id)
    }));
  };

  const updateQuestion = (id: number, field: string, value: any) => {
    setExamData(prev => ({
      ...prev,
      questions: prev.questions.map(q => 
        q.id === id ? { ...q, [field]: value } : q
      )
    }));
  };

  const addStudent = () => {
    if (newStudentEmail && !examData.allowedStudents.includes(newStudentEmail)) {
      setExamData(prev => ({
        ...prev,
        allowedStudents: [...prev.allowedStudents, newStudentEmail]
      }));
      setNewStudentEmail('');
    }
  };

  const removeStudent = (email: string) => {
    setExamData(prev => ({
      ...prev,
      allowedStudents: prev.allowedStudents.filter(e => e !== email)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would typically send the examData to your backend API
    console.log('Creating new exam:', examData);
    // Redirect or show success message
  };

  return (
    <div className="exam-create-page">
      <div className="exam-create-header">
        <h1 className="exam-create-title">
          <Settings size={24} className="header-icon" />
          Create New Exam
        </h1>
        <p className="exam-create-subtitle">Set up questions, timings, rules, and access settings</p>
      </div>

      <form onSubmit={handleSubmit} className="exam-create-container">
        <div className="tab-navigation">
          <button
            type="button"
            onClick={() => setActiveTab('settings')}
            className={`tab-button ${activeTab === 'settings' ? 'active' : ''}`}
          >
            <Settings size={18} className="tab-icon" />
            Settings
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('questions')}
            className={`tab-button ${activeTab === 'questions' ? 'active' : ''}`}
          >
            <BookOpen size={18} className="tab-icon" />
            Questions
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('access')}
            className={`tab-button ${activeTab === 'access' ? 'active' : ''}`}
          >
            <Users size={18} className="tab-icon" />
            Access Control
          </button>
        </div>

        <div className="exam-create-content">
          {activeTab === 'settings' && (
            <div className="settings-section">
              <div className="form-group">
                <label htmlFor="title" className="form-label">Exam Title*</label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={examData.title}
                  onChange={handleInputChange}
                  className="form-input"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="description" className="form-label">Description</label>
                <textarea
                  id="description"
                  name="description"
                  value={examData.description}
                  onChange={handleInputChange}
                  className="form-input"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-group">
                  <label htmlFor="startDate" className="form-label">
                    <Calendar size={16} className="inline-icon" />
                    Start Date & Time*
                  </label>
                  <input
                    type="datetime-local"
                    id="startDate"
                    name="startDate"
                    value={examData.startDate}
                    onChange={handleInputChange}
                    className="form-input"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="endDate" className="form-label">
                    <Calendar size={16} className="inline-icon" />
                    End Date & Time*
                  </label>
                  <input
                    type="datetime-local"
                    id="endDate"
                    name="endDate"
                    value={examData.endDate}
                    onChange={handleInputChange}
                    className="form-input"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-group">
                  <label htmlFor="duration" className="form-label">
                    <Clock size={16} className="inline-icon" />
                    Duration (minutes)*
                  </label>
                  <input
                    type="number"
                    id="duration"
                    name="duration"
                    value={examData.duration}
                    onChange={handleInputChange}
                    className="form-input"
                    min="1"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="passingScore" className="form-label">
                    Passing Score (%)
                  </label>
                  <input
                    type="number"
                    id="passingScore"
                    name="passingScore"
                    value={examData.passingScore}
                    onChange={handleInputChange}
                    className="form-input"
                    min="0"
                    max="100"
                  />
                </div>
              </div>

              <div className="form-checkbox-group">
                <input
                  type="checkbox"
                  id="isTimed"
                  name="isTimed"
                  checked={examData.isTimed}
                  onChange={handleInputChange}
                  className="form-checkbox"
                />
                <label htmlFor="isTimed" className="form-checkbox-label">
                  Enable time limit
                </label>
              </div>

              <div className="form-checkbox-group">
                <input
                  type="checkbox"
                  id="showResults"
                  name="showResults"
                  checked={examData.showResults}
                  onChange={handleInputChange}
                  className="form-checkbox"
                />
                <label htmlFor="showResults" className="form-checkbox-label">
                  Show results to students after completion
                </label>
              </div>

              <div className="form-checkbox-group">
                <input
                  type="checkbox"
                  id="allowRetakes"
                  name="allowRetakes"
                  checked={examData.allowRetakes}
                  onChange={handleInputChange}
                  className="form-checkbox"
                />
                <label htmlFor="allowRetakes" className="form-checkbox-label">
                  Allow multiple attempts
                </label>
              </div>

              <button
                type="button"
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="advanced-toggle"
              >
                {showAdvanced ? (
                  <>
                    <ChevronUp size={16} className="toggle-icon" />
                    Hide Advanced Settings
                  </>
                ) : (
                  <>
                    <ChevronDown size={16} className="toggle-icon" />
                    Show Advanced Settings
                  </>
                )}
              </button>

              {showAdvanced && (
                <div className="advanced-settings">
                  <h3 className="advanced-title">
                    <AlertCircle size={18} className="advanced-icon" />
                    Proctoring Settings
                  </h3>
                  
                  <div className="form-checkbox-group">
                    <input
                      type="checkbox"
                      id="webcam"
                      name="webcam"
                      checked={examData.proctoring.webcam}
                      onChange={handleProctoringChange}
                      className="form-checkbox"
                    />
                    <label htmlFor="webcam" className="form-checkbox-label">
                      Require webcam monitoring
                    </label>
                  </div>

                  <div className="form-checkbox-group">
                    <input
                      type="checkbox"
                      id="microphone"
                      name="microphone"
                      checked={examData.proctoring.microphone}
                      onChange={handleProctoringChange}
                      className="form-checkbox"
                    />
                    <label htmlFor="microphone" className="form-checkbox-label">
                      Require microphone monitoring
                    </label>
                  </div>

                  <div className="form-checkbox-group">
                    <input
                      type="checkbox"
                      id="screenCapture"
                      name="screenCapture"
                      checked={examData.proctoring.screenCapture}
                      onChange={handleProctoringChange}
                      className="form-checkbox"
                    />
                    <label htmlFor="screenCapture" className="form-checkbox-label">
                      Require screen capture
                    </label>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'questions' && (
            <div className="questions-section">
              <div className="questions-list">
                {examData.questions.length === 0 ? (
                  <div className="no-questions">
                    <p>No questions added yet. Add your first question below.</p>
                  </div>
                ) : (
                  examData.questions.map((question, index) => (
                    <div key={question.id} className="question-card">
                      <div className="question-header">
                        <span className="question-number">Question {index + 1}</span>
                        <button
                          type="button"
                          onClick={() => removeQuestion(question.id)}
                          className="delete-question"
                          title="Delete question"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                      
                      <div className="form-group">
                        <label className="form-label">Question Text*</label>
                        <textarea
                          value={question.text}
                          onChange={(e) => updateQuestion(question.id, 'text', e.target.value)}
                          className="form-input"
                          rows={2}
                          required
                          placeholder="Enter question text"
                        />
                      </div>
                      
                      <div className="form-group">
                        <label className="form-label">Question Type*</label>
                        <select
                          value={question.type}
                          onChange={(e) => updateQuestion(question.id, 'type', e.target.value)}
                          className="form-input"
                          required
                          aria-label="Select question type"
                        >
                          <option value="multiple-choice">Multiple Choice</option>
                          <option value="short-answer">Short Answer</option>
                          <option value="essay">Essay</option>
                        </select>
                      </div>
                      
                      <div className="form-group">
                        <label className="form-label" htmlFor={`points-${question.id}`}>Points*</label>
                        <input
                          id={`points-${question.id}`}
                          type="number"
                          value={question.points}
                          onChange={(e) => updateQuestion(question.id, 'points', parseInt(e.target.value))}
                          className="form-input"
                          min="1"
                          required
                          title="Enter the number of points for this question"
                          placeholder="Points"
                        />
                      </div>
                      
                      {question.type === 'multiple-choice' && (
                        <div className="options-section">
                          <label className="form-label">Options*</label>
                          {question.options.map((option, optIndex) => (
                            <div key={optIndex} className="option-input">
                              <input
                                    type="radio"
                                    name={`correctAnswer-${question.id}`}
                                    checked={question.correctAnswer === optIndex}
                                    onChange={() => updateQuestion(question.id, 'correctAnswer', optIndex)}
                                    className="option-radio"
                                    required
                                    title={`Select option ${optIndex + 1} as correct answer`}
                                    placeholder={`Option ${optIndex + 1}`}
                                  />
                              <input
                                type="text"
                                value={option}
                                onChange={(e) => {
                                  const newOptions = [...question.options];
                                  newOptions[optIndex] = e.target.value;
                                  updateQuestion(question.id, 'options', newOptions);
                                }}
                                className="form-input option-text"
                                placeholder={`Option ${optIndex + 1}`}
                                required
                              />
                            </div>
                          ))}
                        </div>
                      )}
                      
                      {question.type === 'short-answer' && (
                        <div className="form-group">
                          <label className="form-label" htmlFor={`correctAnswer-${question.id}`}>Correct Answer</label>
                          <input
                            id={`correctAnswer-${question.id}`}
                            type="text"
                            value={question.correctAnswer}
                            onChange={(e) => updateQuestion(question.id, 'correctAnswer', e.target.value)}
                            className="form-input"
                            title="Enter the correct answer"
                            placeholder="Enter correct answer"
                          />
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
              
              <div className="add-question-section">
                <div className="form-group">
                  <label className="form-label">Add New Question</label>
                  <div className="add-question-controls">
                    <label htmlFor="new-question-type" className="sr-only">
                      Select question type
                    </label>
                    <select
                      id="new-question-type"
                      value={newQuestionType}
                      onChange={(e) => setNewQuestionType(e.target.value)}
                      className="form-input"
                    >
                      <option value="multiple-choice">Multiple Choice</option>
                      <option value="short-answer">Short Answer</option>
                      <option value="essay">Essay</option>
                    </select>
                    <button
                      type="button"
                      onClick={addQuestion}
                      className="add-question-button"
                    >
                      <Plus size={16} className="button-icon" />
                      Add Question
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'access' && (
            <div className="access-section">
              <div className="form-group">
                <label className="form-label">Exam Visibility</label>
                <div className="radio-group">
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="visibility"
                      checked={examData.allowedStudents.length === 0}
                      onChange={() => setExamData(prev => ({ ...prev, allowedStudents: [] }))}
                      className="radio-input"
                    />
                    <span className="radio-custom"></span>
                    Public (All enrolled students)
                  </label>
                  
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="visibility"
                      checked={examData.allowedStudents.length > 0}
                      onChange={() => setExamData(prev => ({ ...prev, allowedStudents: [''] }))}
                      className="radio-input"
                    />
                    <span className="radio-custom"></span>
                    Restricted (Specific students only)
                  </label>
                </div>
              </div>

              {examData.allowedStudents.length > 0 && (
                <div className="student-access">
                  <button
                    type="button"
                    onClick={() => setShowStudentList(!showStudentList)}
                    className="student-list-toggle"
                  >
                    {showStudentList ? (
                      <>
                        <ChevronUp size={16} className="toggle-icon" />
                        Hide Student List ({examData.allowedStudents.length} students)
                      </>
                    ) : (
                      <>
                        <ChevronDown size={16} className="toggle-icon" />
                        Show Student List ({examData.allowedStudents.length} students)
                      </>
                    )}
                  </button>

                  {showStudentList && (
                    <div className="student-list">
                      <div className="add-student">
                        <input
                          type="email"
                          value={newStudentEmail}
                          onChange={(e) => setNewStudentEmail(e.target.value)}
                          placeholder="Enter student email"
                          className="form-input"
                        />
                        <button
                          type="button"
                          onClick={addStudent}
                          className="add-student-button"
                        >
                          <Plus size={16} className="button-icon" />
                          Add
                        </button>
                      </div>

                      <div className="student-list-items">
                        {examData.allowedStudents.map((email, index) => (
                          <div key={index} className="student-item">
                            <span className="student-email">{email}</span>
                            <button
                              type="button"
                              onClick={() => removeStudent(email)}
                              className="remove-student"
                              title="Remove student"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        <div className="exam-create-footer">
          <button type="submit" className="save-button">
            <Save size={18} className="button-icon" />
            Create Exam
          </button>
        </div>
      </form>

      <style>{`
        .exam-create-page {
          padding: 2rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .exam-create-header {
          margin-bottom: 2rem;
        }

        .exam-create-title {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          font-size: 1.875rem;
          font-weight: 700;
          color: #15803d;
          margin-bottom: 0.5rem;
        }

        .header-icon {
          color: #15803d;
        }

        .exam-create-subtitle {
          color: #64748b;
          font-size: 1.125rem;
        }

        .exam-create-container {
          background-color: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }

        .tab-navigation {
          display: flex;
          border-bottom: 1px solid #e2e8f0;
        }

        .tab-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 1rem 1.5rem;
          background: none;
          border: none;
          border-bottom: 2px solid transparent;
          font-weight: 500;
          color: #64748b;
          cursor: pointer;
          transition: all 0.2s;
        }

        .tab-button:hover {
          color: #15803d;
          border-bottom-color: #bbf7d0;
        }

        .tab-button.active {
          color: #15803d;
          border-bottom-color: #15803d;
        }

        .tab-icon {
          margin-right: 0.25rem;
        }

        .exam-create-content {
          padding: 1.5rem;
        }

        .form-group {
          margin-bottom: 1.5rem;
        }

        .form-label {
          display: block;
          font-weight: 500;
          color: #1e293b;
          margin-bottom: 0.5rem;
        }

        .inline-icon {
          margin-right: 0.5rem;
          vertical-align: middle;
        }

        .form-input {
          width: 100%;
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .form-input:focus {
          outline: none;
          border-color: #86efac;
          box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.2);
        }

        textarea.form-input {
          min-height: 2.5rem;
          resize: vertical;
        }

        .form-checkbox-group {
          display: flex;
          align-items: center;
          margin-bottom: 1rem;
        }

        .form-checkbox {
          width: 1rem;
          height: 1rem;
          border: 1px solid #d1d5db;
          border-radius: 0.25rem;
          margin-right: 0.75rem;
          cursor: pointer;
        }

        .form-checkbox:checked {
          background-color: #15803d;
          border-color: #15803d;
        }

        .form-checkbox-label {
          font-size: 0.875rem;
          color: #1e293b;
          cursor: pointer;
        }

        .advanced-toggle {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          background: none;
          border: none;
          color: #15803d;
          font-weight: 500;
          margin: 1.5rem 0;
          cursor: pointer;
          padding: 0;
        }

        .toggle-icon {
          margin-right: 0.25rem;
        }

        .advanced-settings {
          padding: 1.5rem;
          background-color: #f8fafc;
          border-radius: 0.5rem;
          margin-top: 1rem;
        }

        .advanced-title {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 1rem;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 1rem;
        }

        .advanced-icon {
          color: #d97706;
        }

        .questions-list {
          margin-bottom: 2rem;
        }

        .no-questions {
          padding: 2rem;
          text-align: center;
          color: #64748b;
          border: 1px dashed #e2e8f0;
          border-radius: 0.5rem;
        }

        .question-card {
          padding: 1.5rem;
          border: 1px solid #e2e8f0;
          border-radius: 0.5rem;
          margin-bottom: 1.5rem;
          position: relative;
        }

        .question-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .question-number {
          font-weight: 600;
          color: #1e293b;
        }

        .delete-question {
          background: none;
          border: none;
          color: #ef4444;
          cursor: pointer;
          padding: 0.25rem;
        }

        .options-section {
          margin-top: 1rem;
        }

        .option-input {
          display: flex;
          align-items: center;
          margin-bottom: 0.5rem;
        }

        .option-radio {
          margin-right: 0.75rem;
          cursor: pointer;
        }

        .option-text {
          flex: 1;
        }

        .add-question-section {
          padding: 1.5rem;
          background-color: #f8fafc;
          border-radius: 0.5rem;
        }

        .add-question-controls {
          display: flex;
          gap: 0.75rem;
        }

        .add-question-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          background-color: #15803d;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .add-question-button:hover {
          background-color: #166534;
        }

        .button-icon {
          margin-right: 0.25rem;
        }

        .radio-group {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
        }

        .radio-label {
          display: flex;
          align-items: center;
          cursor: pointer;
          font-size: 0.875rem;
        }

        .radio-input {
          position: absolute;
          opacity: 0;
        }

        .radio-custom {
          display: inline-block;
          width: 1rem;
          height: 1rem;
          border: 1px solid #d1d5db;
          border-radius: 50%;
          margin-right: 0.75rem;
          position: relative;
        }

        .radio-input:checked + .radio-custom {
          border-color: #15803d;
        }

        .radio-input:checked + .radio-custom::after {
          content: '';
          position: absolute;
          width: 0.5rem;
          height: 0.5rem;
          background-color: #15803d;
          border-radius: 50%;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        .student-access {
          margin-top: 1.5rem;
        }

        .student-list-toggle {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          background: none;
          border: none;
          color: #15803d;
          font-weight: 500;
          margin-bottom: 1rem;
          cursor: pointer;
          padding: 0;
        }

        .student-list {
          border: 1px solid #e2e8f0;
          border-radius: 0.5rem;
          padding: 1rem;
        }

        .add-student {
          display: flex;
          gap: 0.75rem;
          margin-bottom: 1rem;
        }

        .add-student-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          background-color: #15803d;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .add-student-button:hover {
          background-color: #166534;
        }

        .student-list-items {
          max-height: 200px;
          overflow-y: auto;
        }

        .student-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5rem 0;
          border-bottom: 1px solid #e2e8f0;
        }

        .student-item:last-child {
          border-bottom: none;
        }

        .student-email {
          font-size: 0.875rem;
        }

        .remove-student {
          background: none;
          border: none;
          color: #ef4444;
          cursor: pointer;
          padding: 0.25rem;
        }

        .exam-create-footer {
          padding: 1.5rem;
          border-top: 1px solid #e2e8f0;
          display: flex;
          justify-content: flex-end;
        }

        .save-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1.5rem;
          background-color: #15803d;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .save-button:hover {
          background-color: #166534;
        }

        @media (max-width: 768px) {
          .add-question-controls {
            flex-direction: column;
          }
          
          .add-student {
            flex-direction: column;
          }
        }
      `}</style>
    </div>
  );
};

export default CreateNewExam;