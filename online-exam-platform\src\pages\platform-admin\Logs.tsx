const PlatformAdminLogs = () => {
  const platformLogs = [
    { time: '2025-05-23 10:30 AM', event: 'Platform Admin login - Tresor S.', status: 'Success' },
    { time: '2025-05-23 09:00 AM', event: 'New tenant registered - New University C', status: 'Info' },
    { time: '2025-05-22 03:15 PM', event: 'Subscription status updated for Global School', status: 'Warning' },
  ];

  return (
    <div>
      <h1 className="text-2xl font-bold text-indigo-700 mb-4">Platform Audit Logs</h1>
      <div className="bg-white rounded shadow divide-y">
        {platformLogs.map((log, idx) => (
          <div key={idx} className="p-4 flex justify-between items-center">
            <span className="text-gray-600">{log.time}</span>
            <span>{log.event}</span>
            <span className={`text-sm font-semibold ${log.status === 'Success' ? 'text-green-600' : log.status === 'Warning' ? 'text-orange-600' : 'text-gray-500'}`}>
              {log.status}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PlatformAdminLogs;