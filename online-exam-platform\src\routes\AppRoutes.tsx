import { BrowserRouter, Routes, Route } from 'react-router-dom';
{/* Public Pages */}
import Landing from '../pages/public/Landing';
import Signup  from '../pages/public/Signup';
import Login   from '../pages/public/Login';
import ForgotPassword from '../pages/public/ForgotPassword';
import Payment from '../pages/public/Payment';


{/* Onboarding */}
import ProfileSetup from '../pages/onboarding/ProfileSetup';
import Devi<PERSON><PERSON>heck from '../pages/onboarding/DeviceCheck';

{/* Legal Pages */}
import TermsOfService from '../pages/Legal pages/TermsOfService';
import PrivacyPolicy from '../pages/Legal pages/PrivacyPolicy';

{/* Student */}
import MainLayout     from '../layouts/StudentLayout';
import Dashboard      from '../pages/student/Dashboard';
import TakeExam       from '../pages/student/TakeExam';
import Submitted      from '../pages/student/Submitted';
import Results        from '../pages/student/Results';
import ExamInstructions from '../pages/student/ExamInstructions';
import Profile        from '../pages/student/Profile';
import Help          from '../pages/student/Help';

{/*instructor*/}
import InstructorLayout from '../layouts/InstructorLayout';
import Dashboard2 from '../pages/instructor/Dashboard';
import CreateExam from '../pages/instructor/CreateExam';
import EditExam from '../pages/instructor/EditExam';
import QuestionBank from '../pages/instructor/QuestionBank';
import ExamAnalytics from '../pages/instructor/ExamAnalytics';
import InstructorProfile from '../pages/instructor/Profile';
import InstructorHelp from '../pages/instructor/Help';

{/* Proctor */}
import ProctorLayout from '../layouts/ProctorLayout';
import ProctorDashboard from '../pages/proctor/Dashboard';
import SessionDetail from '../pages/proctor/SessionDetail';
import FlagReview from '../pages/proctor/FlagReview';

{/* Admin */}
import AdminLayout from '../layouts/AdminLayout';
import AdminDashboard from '../pages/admin/Dashboard';
import Users from '../pages/admin/Users';
import Roles from '../pages/admin/Roles';
import Settings from '../pages/admin/Settings';
import Logs from '../pages/admin/Logs';

{/* Platform Admin */}
import PlatformAdminLayout from '../layouts/PlatformAdminLayout';
import PlatformAdminDashboard from '../pages/platform-admin/Dashboard';
import Users2 from '../pages/platform-admin/Users';
import PlatformAdminLogs from '../pages/platform-admin/Logs';
import PlatformAdminSettings from '../pages/platform-admin/Settings';
import PlatformAdminRoles from '../pages/platform-admin/Roles';
import Billing from '../pages/platform-admin/Billing';
import SupportRequests from '../pages/platform-admin/Support';
import Tenants from '../pages/platform-admin/Tenants';

{/* Shared */}

const AppRoutes = () => (
  <BrowserRouter>
    <Routes>
      {/* Public */}
      <Route path="/"        element={<Landing />} />
      <Route path="/signup"  element={<Signup />} />
      <Route path="/login"   element={<Login />} />
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/terms-of-service" element={<TermsOfService />} />
      <Route path="/privacy-policy"   element={<PrivacyPolicy />} />
      <Route path="/payment" element={<Payment/>} />
      <Route path="/profile-setup"    element={<ProfileSetup />} />
      <Route path="/device-check"     element={<DeviceCheck />} />

      {/* Protected / Admin Area */}
      <Route path="/admin" element={<AdminLayout />}>
        <Route path="dashboard" element={<AdminDashboard />} />
        <Route path="users"     element={<Users />} />
        <Route path="roles"     element={<Roles />} />
        <Route path="settings"  element={<Settings />} />
        <Route path="logs"     element={<Logs />} />
      </Route>
      {/* Protected / Platform Admin Area */}
      <Route path="/platform-admin" element={<PlatformAdminLayout />}>
        <Route path="dashboard" element={<PlatformAdminDashboard />} />
        <Route path="users"     element={<Users2 />} />
        <Route path="roles"     element={<PlatformAdminRoles />} />
        <Route path="logs"     element={<PlatformAdminLogs />} />
        <Route path="billing"  element={<Billing />} />
        <Route path="settings"  element={<PlatformAdminSettings />} />
        <Route path="support"  element={<SupportRequests />} />
        <Route path="tenants"  element={<Tenants />} />
      </Route>
      {/* Protected / Student Area */}
      <Route path="/student" element={<MainLayout />}>
        <Route path="dashboard" element={<Dashboard />} />
        <Route path="exam/:examId/info" element={<ExamInstructions />} />
        <Route path="exam/:examId/take" element={<TakeExam />} />
        <Route path="exam/:examId/submitted" element={<Submitted />} />
        <Route path="results" element={<Results />} />
        <Route path="profile" element={<Profile />} />
        <Route path="help" element={<Help />} />
        </Route>

      {/* Protected / Instructor Area */}
      <Route path="/instructor" element={<InstructorLayout />}>
        <Route path="dashboard" element={<Dashboard2 />} />
        <Route path="exam/new"  element={<CreateExam />} />
        <Route path="exam/:examId/edit" element={<EditExam />} />
        <Route path="questions" element={<QuestionBank />} />
        <Route path="exam/:examId/analytics" element={<ExamAnalytics />} />
        <Route path="profile" element={<InstructorProfile />} />
        <Route path="help" element={<InstructorHelp />} />
      </Route>

      {/* Protected / Proctor Area */}
      <Route path="/proctor" element={<ProctorLayout />}>
        <Route path="dashboard" element={<ProctorDashboard />} />
        <Route path="session/:sessionId" element={<SessionDetail />} />
        <Route path="flags" element={<FlagReview />} />
        </Route>

      {/* Fallback */}
      <Route path="*" element={<div className="p-6">Page Not Found</div>} />
    </Routes>
  </BrowserRouter>
);

export default AppRoutes;
