import { useState } from 'react';
import { PencilIcon, TrashIcon, EyeIcon, CheckIcon, XIcon, SearchIcon, PlusIcon, ChevronsUpDownIcon } from 'lucide-react';

type User = {
  id: number;
  name: string;
  email: string;
  role: string;
  status: string;
  lastActive: string;
};

const Users = () => {
  const [users, setUsers] = useState<User[]>([
    { id: 1, name: 'Tresor', email: '<EMAIL>', role: 'Instructor', status: 'Active', lastActive: '2023-05-15' },
    { id: 2, name: 'Fayssa<PERSON>', email: '<EMAIL>', role: 'Student', status: 'Inactive', lastActive: '2023-04-20' },
    { id: 3, name: 'Alice', email: '<EMAIL>', role: 'Admin', status: 'Active', lastActive: '2023-05-10' },
    { id: 4, name: '<PERSON>', email: '<EMAIL>', role: 'Student', status: 'Active', lastActive: '2023-05-12' },
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [newUser, setNewUser] = useState<Omit<User, 'id' | 'lastActive'>>({
    name: '',
    email: '',
    role: 'Student',
    status: 'Active'
  });
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [sortConfig, setSortConfig] = useState<{ key: keyof User, direction: 'asc' | 'desc' }>({ key: 'name', direction: 'asc' });

  const roles = ['Admin', 'Instructor', 'Student'];
  const statuses = ['Active', 'Inactive', 'Suspended'];

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedUsers = [...filteredUsers].sort((a, b) => {
    if (a[sortConfig.key] < b[sortConfig.key]) {
      return sortConfig.direction === 'asc' ? -1 : 1;
    }
    if (a[sortConfig.key] > b[sortConfig.key]) {
      return sortConfig.direction === 'asc' ? 1 : -1;
    }
    return 0;
  });

  const requestSort = (key: keyof User) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

interface HandleEditFn {
    (user: User): void;
}

const handleEdit: HandleEditFn = (user) => {
    setEditingUser({ ...user });
};

  const handleSave = () => {
    if (!editingUser) return;
    setUsers(users.map(user => user.id === editingUser.id ? editingUser : user));
    setEditingUser(null);
  };

  const handleAddUser = () => {
    const newId = Math.max(...users.map(u => u.id), 0) + 1;
    setUsers([...users, { ...newUser, id: newId, lastActive: new Date().toISOString().split('T')[0] }]);
    setNewUser({ name: '', email: '', role: 'Student', status: 'Active' });
    setShowAddModal(false);
  };

interface ConfirmDeleteFn {
    (user: User): void;
}

const confirmDelete: ConfirmDeleteFn = (user) => {
    setUserToDelete(user);
    setShowDeleteModal(true);
};

  const handleDelete = () => {
    if (userToDelete) {
      setUsers(users.filter(user => user.id !== userToDelete.id));
    }
    setShowDeleteModal(false);
    setUserToDelete(null);
  };

interface ToggleStatusFn {
    (userId: number): void;
}

const toggleStatus: ToggleStatusFn = (userId) => {
    setUsers(users.map(user =>
        user.id === userId
            ? { ...user, status: user.status === 'Active' ? 'Inactive' : 'Active' }
            : user
    ));
};

  return (
    <div className="users-container">
      <div className="users-content">
        <div className="users-header">
          <h1>User Management</h1>
          <button
            onClick={() => setShowAddModal(true)}
            className="add-user-button"
          >
            <PlusIcon className="icon" />
            Add User
          </button>
        </div>

        <div className="users-controls">
          <div className="search-container">
            <SearchIcon className="search-icon" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="filter-controls">
            <select aria-label="Filter by role">
              <option>All Roles</option>
              {roles.map(role => (
                <option key={role}>{role}</option>
              ))}
            </select>
            <select aria-label="Filter by status">
              <option>All Statuses</option>
              {statuses.map(status => (
                <option key={status}>{status}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="users-table-container">
          <table className="users-table">
            <thead>
              <tr>
                <th onClick={() => requestSort('name')}>
                  <div className="sortable-header">
                    Name
                    <ChevronsUpDownIcon className="sort-icon" />
                  </div>
                </th>
                <th onClick={() => requestSort('email')}>
                  <div className="sortable-header">
                    Email
                    <ChevronsUpDownIcon className="sort-icon" />
                  </div>
                </th>
                <th onClick={() => requestSort('role')}>
                  <div className="sortable-header">
                    Role
                    <ChevronsUpDownIcon className="sort-icon" />
                  </div>
                </th>
                <th onClick={() => requestSort('status')}>
                  <div className="sortable-header">
                    Status
                    <ChevronsUpDownIcon className="sort-icon" />
                  </div>
                </th>
                <th onClick={() => requestSort('lastActive')}>
                  <div className="sortable-header">
                    Last Active
                    <ChevronsUpDownIcon className="sort-icon" />
                  </div>
                </th>
                <th className="actions-header">Actions</th>
              </tr>
            </thead>
            <tbody>
              {sortedUsers.map((user) => (
                <tr key={user.id}>
                  <td>
                    {editingUser?.id === user.id ? (
                      <input
                        type="text"
                        value={editingUser.name}
                        onChange={(e) => setEditingUser({...editingUser, name: e.target.value})}
                        placeholder="Enter name"
                      />
                    ) : (
                      <div className="user-name">{user.name}</div>
                    )}
                  </td>
                  <td>
                    {editingUser?.id === user.id ? (
                      <input
                        type="email"
                        value={editingUser.email}
                        onChange={(e) => setEditingUser({...editingUser, email: e.target.value})}
                        placeholder="Enter email"
                        title="Email"
                      />
                    ) : (
                      <div className="user-email">{user.email}</div>
                    )}
                  </td>
                  <td>
                    {editingUser?.id === user.id ? (
                      <select
                        aria-label="User Role"
                        value={editingUser.role}
                        onChange={(e) => setEditingUser({...editingUser, role: e.target.value})}
                      >
                        {roles.map(role => (
                          <option key={role} value={role}>{role}</option>
                        ))}
                      </select>
                    ) : (
                      <span className={`role-badge ${user.role.toLowerCase()}`}>
                        {user.role}
                      </span>
                    )}
                  </td>
                  <td>
                    {editingUser?.id === user.id ? (
                      <select
                        aria-label="User Status"
                        value={editingUser.status}
                        onChange={(e) => setEditingUser({...editingUser, status: e.target.value})}
                      >
                        {statuses.map(status => (
                          <option key={status} value={status}>{status}</option>
                        ))}
                      </select>
                    ) : (
                      <span className={`status-badge ${user.status.toLowerCase()}`}>
                        {user.status}
                      </span>
                    )}
                  </td>
                  <td className="last-active">
                    {user.lastActive}
                  </td>
                  <td className="actions-cell">
                    {editingUser?.id === user.id ? (
                      <div className="edit-actions">
                        <button
                          onClick={handleSave}
                          className="save-button"
                          title="Save"
                        >
                          <CheckIcon className="icon" />
                        </button>
                        <button
                          onClick={() => setEditingUser(null)}
                          className="cancel-button"
                          title="Cancel Edit"
                          aria-label="Cancel Edit"
                        >
                          <XIcon className="icon" />
                        </button>
                      </div>
                    ) : (
                      <div className="user-actions">
                        <button
                          onClick={() => toggleStatus(user.id)}
                          className={`status-button ${user.status === 'Active' ? 'deactivate' : 'activate'}`}
                          title={user.status === 'Active' ? 'Deactivate' : 'Activate'}
                        >
                          {user.status === 'Active' ? (
                            <XIcon className="icon" />
                          ) : (
                            <CheckIcon className="icon" />
                          )}
                        </button>
                        <button
                          onClick={() => handleEdit(user)}
                          className="edit-button"
                          title="Edit"
                        >
                          <PencilIcon className="icon" />
                        </button>
                        <button
                          onClick={() => confirmDelete(user)}
                          className="delete-button"
                          title="Delete"
                        >
                          <TrashIcon className="icon" />
                        </button>
                        <button
                          className="view-button"
                          title="View Details"
                        >
                          <EyeIcon className="icon" />
                          <span className="sr-only">View Details</span>
                        </button>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Add User Modal */}
        {showAddModal && (
          <div className="modal-overlay">
            <div className="modal">
              <div className="modal-content">
                <h2>Add New User</h2>
                <div className="form-group">
                  <label>Name</label>
                  <input
                    type="text"
                    value={newUser.name}
                    onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                    placeholder="Enter name"
                    title="Name"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="new-user-email">Email</label>
                  <input
                    id="new-user-email"
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                    placeholder="Enter email"
                    title="Email"
                  />
                </div>
                <div className="form-group">
                  <label>Role</label>
                  <select
                    aria-label="User Role"
                    value={newUser.role}
                    onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                  >
                    {roles.map(role => (
                      <option key={role} value={role}>{role}</option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>Status</label>
                  <select
                    aria-label="User Status"
                    value={newUser.status}
                    onChange={(e) => setNewUser({...newUser, status: e.target.value})}
                  >
                    {statuses.map(status => (
                      <option key={status} value={status}>{status}</option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="cancel-button"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddUser}
                  className="confirm-button"
                  disabled={!newUser.name || !newUser.email}
                >
                  Add User
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div className="modal-overlay">
            <div className="modal">
              <div className="modal-content">
                <h2>Confirm Deletion</h2>
                <p>
                  Are you sure you want to delete user <span>{userToDelete?.name}</span>? This action cannot be undone.
                </p>
              </div>
              <div className="modal-footer">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="cancel-button"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  className="delete-confirm-button"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      <style>{`
        .users-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          padding: 2rem;
        }

        .users-content {
          max-width: 1200px;
          margin: 0 auto;
        }

        .users-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
        }

        .users-header h1 {
          font-size: 1.75rem;
          font-weight: 700;
          color: #1f2937;
          margin: 0;
        }

        .add-user-button {
          display: flex;
          align-items: center;
          padding: 0.5rem 1rem;
          background: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.5rem;
          font-weight: 500;
          cursor: pointer;
          transition: background 0.2s;
        }

        .add-user-button:hover {
          background: #4338ca;
        }

        .add-user-button .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
        }

        .users-controls {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
        }

        .search-container {
          position: relative;
          width: 16rem;
        }

        .search-icon {
          position: absolute;
          left: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          width: 1.25rem;
          height: 1.25rem;
          color: #6b7280;
        }

        .search-container input {
          width: 100%;
          padding: 0.5rem 0.75rem 0.5rem 2.5rem;
          border: 1px solid #d1d5db;
          border-radius: 0.5rem;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .search-container input:focus {
          outline: none;
          border-color: #818cf8;
          box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
        }

        .filter-controls {
          display: flex;
          gap: 1rem;
        }

        .filter-controls select {
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.5rem;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .filter-controls select:focus {
          outline: none;
          border-color: #818cf8;
          box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
        }

        .users-table-container {
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(0, 0, 0, 0.05);
          overflow: hidden;
        }

        .users-table {
          width: 100%;
          border-collapse: collapse;
        }

        .users-table thead {
          background: #f9fafb;
        }

        .users-table th {
          padding: 0.75rem 1.5rem;
          text-align: left;
          font-size: 0.75rem;
          font-weight: 500;
          color: #6b7280;
          text-transform: uppercase;
          letter-spacing: 0.05em;
          cursor: pointer;
          user-select: none;
        }

        .users-table th:hover {
          background: #f3f4f6;
        }

        .sortable-header {
          display: flex;
          align-items: center;
        }

        .sort-icon {
          width: 1rem;
          height: 1rem;
          margin-left: 0.25rem;
          color: #9ca3af;
        }

        .actions-header {
          text-align: right;
        }

        .users-table td {
          padding: 1rem 1.5rem;
          font-size: 0.875rem;
          border-top: 1px solid #e5e7eb;
        }

        .users-table tr:hover {
          background: #f9fafb;
        }

        .user-name {
          font-weight: 500;
          color: #1f2937;
        }

        .user-email {
          color: #6b7280;
        }

        .role-badge, .status-badge {
          display: inline-block;
          padding: 0.25rem 0.5rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 600;
        }

        .role-badge.admin {
          background: #f3e8ff;
          color: #7e22ce;
        }

        .role-badge.instructor {
          background: #dbeafe;
          color: #1d4ed8;
        }

        .role-badge.student {
          background: #dcfce7;
          color: #166534;
        }

        .status-badge.active {
          background: #dcfce7;
          color: #166534;
        }

        .status-badge.inactive {
          background: #fee2e2;
          color: #b91c1c;
        }

        .status-badge.suspended {
          background: #fef3c7;
          color: #92400e;
        }

        .last-active {
          color: #6b7280;
        }

        .actions-cell {
          text-align: right;
        }

        .user-actions, .edit-actions {
          display: flex;
          justify-content: flex-end;
          gap: 0.5rem;
        }

        .status-button, .edit-button, .delete-button, .view-button,
        .save-button, .cancel-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 2rem;
          height: 2rem;
          border-radius: 50%;
          border: none;
          cursor: pointer;
          transition: all 0.2s;
        }

        .status-button.activate {
          background: #dcfce7;
          color: #166534;
        }

        .status-button.deactivate {
          background: #fee2e2;
          color: #b91c1c;
        }

        .status-button:hover {
          opacity: 0.8;
        }

        .edit-button {
          background: #dbeafe;
          color: #1d4ed8;
        }

        .edit-button:hover {
          background: #bfdbfe;
        }

        .delete-button {
          background: #fee2e2;
          color: #b91c1c;
        }

        .delete-button:hover {
          background: #fecaca;
        }

        .view-button {
          background: #e5e7eb;
          color: #4b5563;
        }

        .view-button:hover {
          background: #d1d5db;
        }

        .save-button {
          background: #dcfce7;
          color: #166534;
        }

        .save-button:hover {
          background: #bbf7d0;
        }

        .cancel-button {
          background: #fee2e2;
          color: #b91c1c;
        }

        .cancel-button:hover {
          background: #fecaca;
        }

        .icon {
          width: 1rem;
          height: 1rem;
        }

        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal {
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          width: 100%;
          max-width: 28rem;
          overflow: hidden;
        }

        .modal-content {
          padding: 1.5rem;
        }

        .modal-content h2 {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1rem;
        }

        .modal-content p {
          color: #6b7280;
          margin-bottom: 1rem;
        }

        .modal-content p span {
          font-weight: 500;
          color: #1f2937;
        }

        .form-group {
          margin-bottom: 1rem;
        }

        .form-group label {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #374151;
          margin-bottom: 0.5rem;
        }

        .form-group input, .form-group select {
          width: 100%;
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .form-group input:focus, .form-group select:focus {
          outline: none;
          border-color: #818cf8;
          box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
        }

        .modal-footer {
          display: flex;
          justify-content: flex-end;
          gap: 0.75rem;
          padding: 1rem 1.5rem;
          background: #f9fafb;
          border-top: 1px solid #e5e7eb;
        }

        .modal-footer button {
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
          font-weight: 500;
          font-size: 0.875rem;
          cursor: pointer;
          transition: all 0.2s;
        }

        .cancel-button {
          border: 1px solid #d1d5db;
          background: white;
          color: #374151;
        }

        .cancel-button:hover {
          background: #f3f4f6;
        }

        .confirm-button {
          background: #4f46e5;
          color: white;
          border: none;
        }

        .confirm-button:hover {
          background: #4338ca;
        }

        .confirm-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .delete-confirm-button {
          background: #ef4444;
          color: white;
          border: none;
        }

        .delete-confirm-button:hover {
          background: #dc2626;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .users-controls {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
          }

          .search-container {
            width: 100%;
          }

          .filter-controls {
            width: 100%;
          }

          .filter-controls select {
            flex: 1;
          }
        }
        @media (max-width: 640px) {
          .users-header {
            flex-direction: column;
            align-items: flex-start;
          }

          .add-user-button {
            width: 100%;
            margin-top: 1rem;
          }
        }
        @media (max-width: 480px) {
          .users-header h1 {
            font-size: 1.5rem;
          }

          .users-table th, .users-table td {
            padding: 0.75rem 1rem;
          }

          .users-table th {
            font-size: 0.875rem;
          }

          .users-table td {
            font-size: 0.875rem;
          }
        }

        @media (max-width: 360px) {
          .users-header h1 {
            font-size: 1.25rem;
          }

          .users-table th, .users-table td {
            padding: 0.5rem 0.75rem;
          }

          .users-table th {
            font-size: 0.75rem;
          }

          .users-table td {
            font-size: 0.75rem;
          }
        }
      `}</style>
    </div>
  );
};

export default Users;