import { useState } from 'react';
import { SearchIcon, DownloadIcon, FilterIcon, CalendarIcon, RefreshCwIcon } from 'lucide-react';

const Logs = () => {
  const [logs, setLogs] = useState([
    { 
      id: 1, 
      timestamp: '2023-06-15T14:32:45Z', 
      event: 'User login', 
      user: '<EMAIL>', 
      ip: '***********', 
      status: 'Success', 
      severity: 'Info',
      details: 'Successful authentication via OAuth2'
    },
    { 
      id: 2, 
      timestamp: '2023-06-15T13:45:12Z', 
      event: 'Role updated', 
      user: '<EMAIL>', 
      ip: '************', 
      status: 'Completed', 
      severity: 'Medium',
      details: 'Changed role from Student to Instructor'
    },
    { 
      id: 3, 
      timestamp: '2023-06-15T12:18:33Z', 
      event: 'Failed login attempt', 
      user: '<EMAIL>', 
      ip: '************', 
      status: 'Failed', 
      severity: 'High',
      details: '5 failed attempts from this IP'
    },
    { 
      id: 4, 
      timestamp: '2023-06-15T10:05:21Z', 
      event: 'Data export', 
      user: '<EMAIL>', 
      ip: '*************', 
      status: 'Completed', 
      severity: 'Low',
      details: 'Exported user data in CSV format'
    },
    { 
      id: 5, 
      timestamp: '2023-06-15T04:00:00Z', 
      event: 'System backup', 
      user: 'system', 
      ip: '', 
      status: 'Completed', 
      severity: 'Info',
      details: 'Nightly database backup to S3'
    },
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSeverity, setSelectedSeverity] = useState('All');
  const [selectedEventType, setSelectedEventType] = useState('All');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [expandedLog, setExpandedLog] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const severityOptions = ['All', 'Info', 'Low', 'Medium', 'High', 'Critical'];
  const eventTypeOptions = ['All', 'User login', 'Role updated', 'Failed login', 'Data export', 'System backup'];

  const filteredLogs = logs.filter(log => {
    const matchesSearch = 
      log.event.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.details.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesSeverity = selectedSeverity === 'All' || log.severity === selectedSeverity;
    const matchesEventType = selectedEventType === 'All' || log.event === selectedEventType;
    
    const matchesDateRange = 
      (!dateRange.start || new Date(log.timestamp) >= new Date(dateRange.start)) &&
      (!dateRange.end || new Date(log.timestamp) <= new Date(dateRange.end));
    
    return matchesSearch && matchesSeverity && matchesEventType && matchesDateRange;
  });

  const refreshLogs = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const exportLogs = () => {
    // In a real app, this would generate a CSV/JSON file
    alert('Exporting logs...');
  };

interface Log {
    id: number;
    timestamp: string;
    event: string;
    user: string;
    ip: string;
    status: string;
    severity: string;
    details: string;
}

interface DateRange {
    start: string;
    end: string;
}

const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleString();
};

interface SeverityColorMap {
    [key: string]: string;
}

const getSeverityColor = (severity: string): string => {
    const colorMap: SeverityColorMap = {
        High: 'bg-red-100 text-red-800',
        Medium: 'bg-yellow-100 text-yellow-800',
        Low: 'bg-blue-100 text-blue-800',
        Critical: 'bg-purple-100 text-purple-800',
        Info: 'bg-green-100 text-green-800',
    };
    return colorMap[severity] || 'bg-green-100 text-green-800';
};

  return (
    <div className="logs-container">
      <div className="logs-content">
        <div className="logs-header">
          <h1>Audit Logs & Security Events</h1>
          <div className="logs-actions">
            <button 
              className="refresh-button"
              onClick={refreshLogs}
              disabled={isLoading}
            >
              <RefreshCwIcon className="icon" />
              {isLoading ? 'Refreshing...' : 'Refresh'}
            </button>
            <button 
              className="export-button"
              onClick={exportLogs}
            >
              <DownloadIcon className="icon" />
              Export
            </button>
          </div>
        </div>

        <div className="logs-filters">
          <div className="search-container">
            <SearchIcon className="search-icon" />
            <input
              type="text"
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="filter-group">
            <FilterIcon className="filter-icon" />
            <select
              aria-label="Filter by severity"
              value={selectedSeverity}
              onChange={(e) => setSelectedSeverity(e.target.value)}
            >
              {severityOptions.map(option => (
                <option key={option} value={option}>{option} Severity</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <FilterIcon className="filter-icon" />
            <select
              aria-label="Filter by event type"
              value={selectedEventType}
              onChange={(e) => setSelectedEventType(e.target.value)}
            >
              {eventTypeOptions.map(option => (
                <option key={option} value={option}>{option === 'All' ? 'All Events' : option}</option>
              ))}
            </select>
          </div>

          <div className="date-range-filter">
            <CalendarIcon className="calendar-icon" />
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange({...dateRange, start: e.target.value})}
              placeholder="Start date"
            />
            <span>to</span>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange({...dateRange, end: e.target.value})}
              placeholder="End date"
            />
          </div>
        </div>

        <div className="logs-table-container">
          <table className="logs-table">
            <thead>
              <tr>
                <th>Timestamp</th>
                <th>Event</th>
                <th>User</th>
                <th>IP Address</th>
                <th>Status</th>
                <th>Severity</th>
              </tr>
            </thead>
            <tbody>
              {filteredLogs.map(log => (
                <>
                  <tr 
                    key={log.id} 
                    className="log-row"
                    onClick={() => setExpandedLog(expandedLog === log.id ? null : log.id)}
                  >
                    <td>{formatDate(log.timestamp)}</td>
                    <td>{log.event}</td>
                    <td>{log.user}</td>
                    <td>{log.ip || 'N/A'}</td>
                    <td>
                      <span className={`status-badge ${log.status.toLowerCase()}`}>
                        {log.status}
                      </span>
                    </td>
                    <td>
                      <span className={`severity-badge ${getSeverityColor(log.severity)}`}>
                        {log.severity}
                      </span>
                    </td>
                  </tr>
                  {expandedLog === log.id && (
                    <tr className="details-row">
                      <td colSpan={6}>
                        <div className="log-details">
                          <h4>Event Details</h4>
                          <p>{log.details}</p>
                          <div className="details-grid">
                            <div>
                              <span className="detail-label">User Agent:</span>
                              <span>Mozilla/5.0 (Windows NT 10.0; Win64; x64)</span>
                            </div>
                            <div>
                              <span className="detail-label">Location:</span>
                              <span>New York, US (estimated)</span>
                            </div>
                            <div>
                              <span className="detail-label">Session ID:</span>
                              <span>a1b2c3d4e5f6</span>
                            </div>
                            <div>
                              <span className="detail-label">Affected Resources:</span>
                              <span>{log.event === 'Role updated' ? 'User ID 42' : 'N/A'}</span>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </>
              ))}
            </tbody>
          </table>
          {filteredLogs.length === 0 && (
            <div className="no-results">
              No logs match your current filters
            </div>
          )}
        </div>
      </div>

      <style>{`
        .logs-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          padding: 2rem;
        }

        .logs-content {
          max-width: 1200px;
          margin: 0 auto;
        }

        .logs-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
        }

        .logs-header h1 {
          font-size: 1.75rem;
          font-weight: 700;
          color: #1f2937;
          margin: 0;
        }

        .logs-actions {
          display: flex;
          gap: 0.75rem;
        }

        .refresh-button, .export-button {
          display: flex;
          align-items: center;
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .refresh-button {
          background: #f3f4f6;
          color: #4b5563;
          border: none;
        }

        .refresh-button:hover {
          background: #e5e7eb;
        }

        .refresh-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .export-button {
          background: #4f46e5;
          color: white;
          border: none;
        }

        .export-button:hover {
          background: #4338ca;
        }

        .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
        }

        .logs-filters {
          display: flex;
          flex-wrap: wrap;
          gap: 1rem;
          margin-bottom: 1.5rem;
          background: white;
          padding: 1rem;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .search-container {
          position: relative;
          flex: 1;
          min-width: 200px;
        }

        .search-icon {
          position: absolute;
          left: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          width: 1.25rem;
          height: 1.25rem;
          color: #6b7280;
        }

        .search-container input {
          width: 100%;
          padding: 0.5rem 0.75rem 0.5rem 2.5rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .search-container input:focus {
          outline: none;
          border-color: #818cf8;
          box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
        }

        .filter-group {
          position: relative;
          min-width: 180px;
        }

        .filter-icon {
          position: absolute;
          left: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          width: 1.25rem;
          height: 1.25rem;
          color: #6b7280;
          pointer-events: none;
        }

        .filter-group select {
          width: 100%;
          padding: 0.5rem 0.75rem 0.5rem 2.5rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          appearance: none;
          transition: all 0.2s;
        }

        .filter-group select:focus {
          outline: none;
          border-color: #818cf8;
          box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
        }

        .date-range-filter {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          min-width: 300px;
        }

        .calendar-icon {
          width: 1.25rem;
          height: 1.25rem;
          color: #6b7280;
        }

        .date-range-filter input {
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .date-range-filter input:focus {
          outline: none;
          border-color: #818cf8;
          box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
        }

        .date-range-filter span {
          font-size: 0.875rem;
          color: #6b7280;
        }

        .logs-table-container {
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(0, 0, 0, 0.05);
          overflow: hidden;
        }

        .logs-table {
          width: 100%;
          border-collapse: collapse;
        }

        .logs-table th {
          padding: 1rem;
          text-align: left;
          font-size: 0.75rem;
          font-weight: 500;
          color: #6b7280;
          text-transform: uppercase;
          letter-spacing: 0.05em;
          background: #f9fafb;
          border-bottom: 1px solid #e5e7eb;
        }

        .log-row {
          cursor: pointer;
          transition: background 0.2s;
        }

        .log-row:hover {
          background: #f9fafb;
        }

        .logs-table td {
          padding: 1rem;
          font-size: 0.875rem;
          border-bottom: 1px solid #e5e7eb;
        }

        .status-badge {
          display: inline-block;
          padding: 0.25rem 0.5rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 600;
        }

        .status-badge.success {
          background: #dcfce7;
          color: #166534;
        }

        .status-badge.completed {
          background: #dbeafe;
          color: #1d4ed8;
        }

        .status-badge.failed {
          background: #fee2e2;
          color: #b91c1c;
        }

        .severity-badge {
          display: inline-block;
          padding: 0.25rem 0.5rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 600;
        }

        .details-row {
          background: #f8fafc;
        }

        .log-details {
          padding: 1rem;
        }

        .log-details h4 {
          font-size: 0.875rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 0.5rem 0;
        }

        .log-details p {
          font-size: 0.875rem;
          color: #4b5563;
          margin: 0 0 1rem 0;
        }

        .details-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 1rem;
        }

        .detail-label {
          display: block;
          font-size: 0.75rem;
          color: #6b7280;
          margin-bottom: 0.25rem;
        }

        .no-results {
          padding: 2rem;
          text-align: center;
          color: #6b7280;
          font-size: 0.875rem;
        }

        @media (max-width: 768px) {
          .logs-filters {
            flex-direction: column;
          }

          .search-container, .filter-group, .date-range-filter {
            width: 100%;
          }

          .date-range-filter {
            flex-direction: column;
            align-items: flex-start;
          }

          .logs-table {
            display: block;
            overflow-x: auto;
          }
        }
      `}</style>
    </div>
  );
};

export default Logs;