import React, { useState, useEffect } from 'react';
import { FiEdit, FiTrash2, FiPlus, FiSearch, FiX, FiCheck, FiInfo, FiLayers, FiDollarSign, FiGlobe, FiAlertTriangle, FiRefreshCcw } from 'react-icons/fi';

// Define types for better type safety
interface Tenant {
  id: number;
  name: string;
  status: 'Active' | 'Pending Setup' | 'Inactive';
  plan: 'Enterprise' | 'Standard' | 'Basic'; // Removed 'Free Trial'
  setupComplete: boolean;
  contactEmail: string; // Added for more realism
  startDate: string; // Added for more realism (ISO date string)
  lastActivity: string; // Added for more realism (ISO date string)
}

// Reusable Modal Component (defined once for both PlatformAdminUsers and Tenants)
interface ModalProps {
  title: string;
  onClose: () => void;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ title, onClose, children }) => {
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>{title}</h2>
          <button
            className="modal-close-button"
            onClick={onClose}
            title="Close modal"
            aria-label="Close modal"
          >
            <FiX size={24} />
          </button>
        </div>
        {children}
      </div>
    </div>
  );
};

const Tenants = () => {
  // Mock data for initial tenant list (no 'Free Trial' plan)
  const initialTenants: Tenant[] = [
    { id: 1, name: 'University A', status: 'Active', plan: 'Enterprise', setupComplete: true, contactEmail: '<EMAIL>', startDate: '2023-01-15', lastActivity: '2025-05-22T10:30:00Z' },
    { id: 2, name: 'University B', status: 'Active', plan: 'Standard', setupComplete: true, contactEmail: '<EMAIL>', startDate: '2023-03-01', lastActivity: '2025-05-23T09:15:00Z' },
    { id: 3, name: 'New University C', status: 'Pending Setup', plan: 'Basic', setupComplete: false, contactEmail: '<EMAIL>', startDate: '2024-11-20', lastActivity: '2024-11-20T14:00:00Z' },
    { id: 4, name: 'Global School', status: 'Inactive', plan: 'Enterprise', setupComplete: true, contactEmail: '<EMAIL>', startDate: '2022-09-10', lastActivity: '2025-04-10T11:00:00Z' },
    { id: 5, name: 'Tech Institute X', status: 'Active', plan: 'Basic', setupComplete: true, contactEmail: '<EMAIL>', startDate: '2024-06-01', lastActivity: '2025-05-21T16:45:00Z' },
  ];

  // State management for tenants and UI interactions
  const [tenants, setTenants] = useState<Tenant[]>(initialTenants);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false); // New: for viewing tenant details
  const [currentTenant, setCurrentTenant] = useState<Tenant | null>(null); // For editing, deactivating, or viewing details
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [filterPlan, setFilterPlan] = useState('');
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Form state for Add/Edit Tenant
  const [formData, setFormData] = useState<Omit<Tenant, 'id' | 'startDate' | 'lastActivity'>>({
    name: '',
    status: 'Pending Setup', // Default for new tenants
    plan: 'Basic', // Default for new tenants
    setupComplete: false, // Default for new tenants
    contactEmail: '',
  });

  // --- Helper Functions ---
  const validateForm = () => {
    const errors: Record<string, string> = {};
    if (!formData.name.trim()) errors.name = 'University/Tenant Name is required';
    if (!formData.contactEmail.trim()) errors.contactEmail = 'Contact Email is required';
    else if (!/\S+@\S+\.\S+/.test(formData.contactEmail)) errors.contactEmail = 'Invalid email format';
    if (!formData.status) errors.status = 'Status is required';
    if (!formData.plan) errors.plan = 'Billing Plan is required';
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const resetForm = () => {
    setFormData({ name: '', status: 'Pending Setup', plan: 'Basic', setupComplete: false, contactEmail: '' });
    setFormErrors({});
  };

  const getStatusBadgeClass = (status: 'Active' | 'Pending Setup' | 'Inactive') => {
    switch (status) {
      case 'Active': return 'status-active';
      case 'Pending Setup': return 'status-pending';
      case 'Inactive': return 'status-inactive';
      default: return 'status-default';
    }
  };

  const getPlanBadgeClass = (plan: 'Enterprise' | 'Standard' | 'Basic') => {
    switch (plan) {
      case 'Enterprise': return 'plan-enterprise';
      case 'Standard': return 'plan-standard';
      case 'Basic': return 'plan-basic';
      default: return 'plan-default';
    }
  };

  const getSetupStatusIcon = (isComplete: boolean) => {
    return isComplete ? <FiCheck size={18} className="text-green-success" /> : <FiAlertTriangle size={18} className="text-yellow-warning" />;
  };

  // --- CRUD Operations ---

  // Create Tenant
  const handleAddTenant = () => {
    if (!validateForm()) return;
    const newTenant: Tenant = {
      id: tenants.length > 0 ? Math.max(...tenants.map(t => t.id)) + 1 : 1,
      ...formData,
      status: formData.status as Tenant['status'], // Type assertion based on validation
      plan: formData.plan as Tenant['plan'],
      setupComplete: formData.setupComplete,
      startDate: new Date().toISOString().split('T')[0], // Current date
      lastActivity: new Date().toISOString(), // Current timestamp
    };
    setTenants([...tenants, newTenant]);
    setIsAddModalOpen(false);
    resetForm();
  };

  // View Tenant Details
  const handleViewDetailsClick = (tenant: Tenant) => {
    setCurrentTenant(tenant);
    setIsDetailsModalOpen(true);
  };

  // Edit Tenant (Prepare form)
  const handleEditClick = (tenant: Tenant) => {
    setCurrentTenant(tenant);
    setFormData({
      name: tenant.name,
      status: tenant.status,
      plan: tenant.plan,
      setupComplete: tenant.setupComplete,
      contactEmail: tenant.contactEmail,
    });
    setIsEditModalOpen(true);
  };

  // Update Tenant
  const handleUpdateTenant = () => {
    if (!currentTenant || !validateForm()) return;
    setTenants(tenants.map(tenant =>
      tenant.id === currentTenant.id
        ? {
            ...tenant,
            name: formData.name,
            status: formData.status as Tenant['status'],
            plan: formData.plan as Tenant['plan'],
            setupComplete: formData.setupComplete,
            contactEmail: formData.contactEmail,
            lastActivity: new Date().toISOString(), // Update last activity on edit
          }
        : tenant
    ));
    setIsEditModalOpen(false);
    setCurrentTenant(null);
    resetForm();
  };

  // Change Status (Prepare confirmation)
  const handleChangeStatusClick = (tenant: Tenant) => {
    setCurrentTenant(tenant);
    setIsConfirmModalOpen(true);
  };

  // Confirm Change Status (Deactivate/Activate)
  const handleConfirmStatusChange = () => {
    if (currentTenant) {
      setTenants(tenants.map(tenant =>
        tenant.id === currentTenant.id
          ? { ...tenant, status: currentTenant.status === 'Active' ? 'Inactive' : 'Active', lastActivity: new Date().toISOString() }
          : tenant
      ));
    }
    setIsConfirmModalOpen(false);
    setCurrentTenant(null);
  };

  // --- Filtering and Searching ---
  const filteredTenants = tenants.filter(tenant => {
    const matchesSearch = tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          tenant.contactEmail.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus ? tenant.status === filterStatus : true;
    const matchesPlan = filterPlan ? tenant.plan === filterPlan : true;
    return matchesSearch && matchesStatus && matchesPlan;
  });

  // --- Rendered JSX ---
  return (
    <div className="admin-page-container">
      <div className="admin-page-wrapper">
        <div className="admin-header">
          <h1 className="admin-title">Tenant Management</h1>
          <button
            className="add-button"
            onClick={() => {
              resetForm();
              setIsAddModalOpen(true);
            }}
          >
            <FiPlus size={18} /> Add New Tenant
          </button>
        </div>

        {/* Search and Filter Bar */}
        <div className="filter-bar">
          <div className="search-input-wrapper">
            <FiSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="filter-select"
            aria-label="Filter by status"
          >
            <option value="">All Statuses</option>
            <option value="Active">Active</option>
            <option value="Pending Setup">Pending Setup</option>
            <option value="Inactive">Inactive</option>
          </select>
          <select
            value={filterPlan}
            onChange={(e) => setFilterPlan(e.target.value)}
            className="filter-select"
            aria-label="Filter by plan"
          >
            <option value="">All Plans</option>
            <option value="Enterprise">Enterprise</option>
            <option value="Standard">Standard</option>
            <option value="Basic">Basic</option>
          </select>
          {(searchTerm || filterStatus || filterPlan) && (
            <button className="clear-filters-button" onClick={() => {
              setSearchTerm('');
              setFilterStatus('');
              setFilterPlan('');
            }}>
              Clear Filters <FiX size={16} />
            </button>
          )}
        </div>

        {/* Tenants Table */}
        <div className="table-card">
          <div className="table-wrapper">
            <table>
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Tenant Name</th>
                  <th>Status</th>
                  <th>Plan</th>
                  <th>Setup Complete</th>
                  <th>Start Date</th> {/* New column */}
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredTenants.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="text-center-message">No tenants found matching your criteria.</td>
                  </tr>
                ) : (
                  filteredTenants.map((tenant) => (
                    <tr key={tenant.id}>
                      <td>{tenant.id}</td>
                      <td>{tenant.name}</td>
                      <td>
                        <span className={`status-badge ${getStatusBadgeClass(tenant.status)}`}>
                          {tenant.status}
                        </span>
                      </td>
                      <td>
                        <span className={`plan-badge ${getPlanBadgeClass(tenant.plan)}`}>
                          {tenant.plan}
                        </span>
                      </td>
                      <td>{getSetupStatusIcon(tenant.setupComplete)}</td>
                      <td>{new Date(tenant.startDate).toLocaleDateString()}</td>
                      <td className="action-buttons-cell">
                        <button
                          className="action-button view"
                          onClick={() => handleViewDetailsClick(tenant)}
                          title="View Details"
                        >
                          <FiInfo size={16} />
                        </button>
                        <button
                          className="action-button edit"
                          onClick={() => handleEditClick(tenant)}
                          title="Edit Tenant"
                        >
                          <FiEdit size={16} />
                        </button>
                        <button
                          className="action-button deactivate"
                          onClick={() => handleChangeStatusClick(tenant)}
                          title={tenant.status === 'Active' ? 'Deactivate Tenant' : 'Activate Tenant'}
                        >
                          {tenant.status === 'Active' ? <FiTrash2 size={16} /> : <FiCheck size={16} />}
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Add Tenant Modal */}
        {isAddModalOpen && (
          <Modal title="Add New Tenant" onClose={() => setIsAddModalOpen(false)}>
            <form onSubmit={(e) => { e.preventDefault(); handleAddTenant(); }}>
              <div className="form-grid">
                <div className="form-group">
                  <label>Tenant Name:</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className={formErrors.name ? 'input-error' : ''}
                    placeholder="Enter tenant name"
                    title="Tenant Name"
                  />
                  {formErrors.name && <p className="error-message">{formErrors.name}</p>}
                </div>
                <div className="form-group">
                  <label htmlFor="contactEmailAdd">Contact Email:</label>
                  <input
                    id="contactEmailAdd"
                    type="email"
                    value={formData.contactEmail}
                    onChange={(e) => setFormData({ ...formData, contactEmail: e.target.value })}
                    className={formErrors.contactEmail ? 'input-error' : ''}
                    placeholder="Enter contact email"
                    title="Contact Email"
                  />
                  {formErrors.contactEmail && <p className="error-message">{formErrors.contactEmail}</p>}
                </div>
                <div className="form-group">
                  <label>Status:</label>
                  <select
                    id="statusAdd"
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value as Tenant['status'] })}
                    className={formErrors.status ? 'input-error' : ''}
                    aria-label="Status"
                    title="Status"
                  >
                    <option value="">Select Status</option>
                    <option value="Active">Active</option>
                    <option value="Pending Setup">Pending Setup</option>
                    <option value="Inactive">Inactive</option>
                  </select>
                  {formErrors.status && <p className="error-message">{formErrors.status}</p>}
                </div>
                <div className="form-group">
                  <label>Billing Plan:</label>
                  <select
                    value={formData.plan}
                    onChange={(e) => setFormData({ ...formData, plan: e.target.value as Tenant['plan'] })}
                    className={formErrors.plan ? 'input-error' : ''}
                    aria-label="Billing Plan"
                    title="Billing Plan"
                  >
                    <option value="">Select Plan</option>
                    <option value="Enterprise">Enterprise</option>
                    <option value="Standard">Standard</option>
                    <option value="Basic">Basic</option>
                  </select>
                  {formErrors.plan && <p className="error-message">{formErrors.plan}</p>}
                </div>
                <div className="form-group checkbox-group">
                  <input
                    type="checkbox"
                    id="setupCompleteAdd"
                    checked={formData.setupComplete}
                    onChange={(e) => setFormData({ ...formData, setupComplete: e.target.checked })}
                  />
                  <label htmlFor="setupCompleteAdd">Setup Complete</label>
                </div>
              </div>
              <div className="modal-actions">
                <button type="button" className="button-secondary" onClick={() => setIsAddModalOpen(false)}>
                  Cancel
                </button>
                <button type="submit" className="button-primary">
                  <FiPlus size={18} /> Add Tenant
                </button>
              </div>
            </form>
          </Modal>
        )}

        {/* Edit Tenant Modal */}
        {isEditModalOpen && currentTenant && (
          <Modal title={`Edit Tenant: ${currentTenant.name}`} onClose={() => setIsEditModalOpen(false)}>
            <form onSubmit={(e) => { e.preventDefault(); handleUpdateTenant(); }}>
              <div className="form-grid">
                <div className="form-group">
                  <label>Tenant Name:</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className={formErrors.name ? 'input-error' : ''}
                    title="Tenant Name"
                    placeholder="Enter tenant name"
                  />
                  {formErrors.name && <p className="error-message">{formErrors.name}</p>}
                </div>
                <div className="form-group">
                  <label>Contact Email:</label>
                  <input
                    type="email"
                    value={formData.contactEmail}
                    onChange={(e) => setFormData({ ...formData, contactEmail: e.target.value })}
                    className={formErrors.contactEmail ? 'input-error' : ''}
                    title="Contact Email"
                    placeholder="Enter contact email"
                  />
                  {formErrors.contactEmail && <p className="error-message">{formErrors.contactEmail}</p>}
                </div>
                <div className="form-group">
                  <label>Status:</label>
                <select
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value as Tenant['status'] })}
                    className={formErrors.status ? 'input-error' : ''}
                    aria-label="Status"
                    title="Status"
                >
                    <option value="">Select Status</option>
                    <option value="Active">Active</option>
                    <option value="Pending Setup">Pending Setup</option>
                    <option value="Inactive">Inactive</option>
                </select>
                  {formErrors.status && <p className="error-message">{formErrors.status}</p>}
                </div>
                <div className="form-group">
                  <label>Billing Plan:</label>
                  <select
                    value={formData.plan}
                    onChange={(e) => setFormData({ ...formData, plan: e.target.value as Tenant['plan'] })}
                    className={formErrors.plan ? 'input-error' : ''}
                    aria-label="Billing Plan"
                    title="Billing Plan"
                  >
                    <option value="">Select Plan</option>
                    <option value="Enterprise">Enterprise</option>
                    <option value="Standard">Standard</option>
                    <option value="Basic">Basic</option>
                  </select>
                  {formErrors.plan && <p className="error-message">{formErrors.plan}</p>}
                </div>
                <div className="form-group checkbox-group">
                  <input
                    type="checkbox"
                    id="setupCompleteEdit"
                    checked={formData.setupComplete}
                    onChange={(e) => setFormData({ ...formData, setupComplete: e.target.checked })}
                  />
                  <label htmlFor="setupCompleteEdit">Setup Complete</label>
                </div>
              </div>
              <div className="modal-actions">
                <button type="button" className="button-secondary" onClick={() => setIsEditModalOpen(false)}>
                  Cancel
                </button>
                <button type="submit" className="button-primary">
                  <FiCheck size={18} /> Update Tenant
                </button>
              </div>
            </form>
          </Modal>
        )}

        {/* Deactivation/Activation Confirmation Modal */}
        {isConfirmModalOpen && currentTenant && (
          <Modal title={`Confirm Status Change for ${currentTenant.name}`} onClose={() => setIsConfirmModalOpen(false)}>
            <p className="modal-message">
              Are you sure you want to change the status of **{currentTenant.name}** to **{currentTenant.status === 'Active' ? 'Inactive' : 'Active'}**?
            </p>
            <div className="modal-actions">
              <button type="button" className="button-secondary" onClick={() => setIsConfirmModalOpen(false)}>
                Cancel
              </button>
              <button
                type="button"
                className={currentTenant.status === 'Active' ? 'button-danger' : 'button-success'}
                onClick={handleConfirmStatusChange}
              >
                {currentTenant.status === 'Active' ? <FiTrash2 size={18} /> : <FiCheck size={18} />}
                {currentTenant.status === 'Active' ? 'Deactivate' : 'Activate'}
              </button>
            </div>
          </Modal>
        )}

        {/* View Tenant Details Modal */}
        {isDetailsModalOpen && currentTenant && (
          <Modal title={`Tenant Details: ${currentTenant.name}`} onClose={() => setIsDetailsModalOpen(false)}>
            <div className="details-grid">
              <div className="detail-item">
                <span className="detail-label">ID:</span>
                <span className="detail-value">{currentTenant.id}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Name:</span>
                <span className="detail-value">{currentTenant.name}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Contact Email:</span>
                <span className="detail-value">{currentTenant.contactEmail}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Status:</span>
                <span className={`status-badge ${getStatusBadgeClass(currentTenant.status)}`}>{currentTenant.status}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Billing Plan:</span>
                <span className={`plan-badge ${getPlanBadgeClass(currentTenant.plan)}`}>{currentTenant.plan}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Setup Complete:</span>
                <span className="detail-value">{getSetupStatusIcon(currentTenant.setupComplete)} {currentTenant.setupComplete ? 'Yes' : 'No'}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Start Date:</span>
                <span className="detail-value">{new Date(currentTenant.startDate).toLocaleDateString()}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Last Activity:</span>
                <span className="detail-value">{new Date(currentTenant.lastActivity).toLocaleString()}</span>
              </div>
              {/* You can add more details here, e.g., associated university admins, usage stats */}
            </div>
            <div className="modal-actions">
              <button type="button" className="button-secondary" onClick={() => setIsDetailsModalOpen(false)}>
                Close
              </button>
              <button type="button" className="button-primary" onClick={() => {
                setIsDetailsModalOpen(false);
                handleEditClick(currentTenant);
              }}>
                <FiEdit size={18} /> Edit Tenant
              </button>
            </div>
          </Modal>
        )}
      </div>

      {/* --- Inline Styles for Tenants Component --- */}
      <style>{`
        /* Global Variables (consistent with dashboard and user management) */
        :root {
            --primary-color: #4f46e5;      /* Indigo-600 */
            --primary-dark: #4338ca;       /* Indigo-700 */
            --primary-light: #eef2ff;      /* Indigo-50 */
            --green-success: #10b981;      /* Emerald-500 */
            --green-light: #ecfdf5;        /* Emerald-50 */
            --red-danger: #ef4444;         /* Red-500 */
            --red-light: #fee2e2;          /* Red-50 */
            --yellow-warning: #f59e0b;     /* Amber-500 */
            --yellow-light: #fffbeb;       /* Amber-50 */
            --blue-info: #3b82f6;          /* Blue-500 */
            --blue-light: #eff6ff;         /* Blue-50 */
            --purple-accent: #8b5cf6;      /* Purple-500 */
            --purple-light: #f5f3ff;       /* Purple-50 */
            
            --text-dark: #1f2937;          /* Gray-900 */
            --text-medium: #4b5563;        /* Gray-700 */
            --text-light: #6b7280;         /* Gray-500 */
            --border-color: #e5e7eb;       /* Gray-200 */
            --bg-light: #f9fafb;           /* Gray-50 */
            --bg-gradient-start: #f0f4ff;
            --bg-gradient-end: #e6f0ff;
            
            --card-bg: white;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
        }

        .admin-page-container {
          min-height: 100vh;
          background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
          padding: 2.5rem 2rem;
          font-family: 'Inter', sans-serif;
          color: var(--text-dark);
        }

        .admin-page-wrapper {
          max-width: 1280px;
          margin: 0 auto;
        }

        /* Header */
        .admin-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
          background: var(--card-bg);
          padding: 1.5rem 2rem;
          border-radius: 0.75rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
        }

        .admin-title {
          font-size: 2rem;
          font-weight: 800;
          color: var(--primary-dark);
          letter-spacing: -0.03em;
          margin: 0;
        }

        .add-button { /* Reusing add-user-button styles, renamed for generality */
          background: var(--primary-color);
          color: white;
          padding: 0.8rem 1.5rem;
          border-radius: 0.5rem;
          border: none;
          font-weight: 600;
          font-size: 1rem;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          transition: background 0.2s ease-in-out, transform 0.1s ease-in-out;
          box-shadow: var(--shadow-sm);
        }

        .add-button:hover {
          background: var(--primary-dark);
          transform: translateY(-1px);
        }

        /* Filter Bar */
        .filter-bar {
          display: flex;
          gap: 1rem;
          margin-bottom: 1.5rem;
          background: var(--card-bg);
          padding: 1.2rem 1.5rem;
          border-radius: 0.75rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
          flex-wrap: wrap;
          align-items: center;
        }

        .search-input-wrapper {
          position: relative;
          flex-grow: 1;
          min-width: 200px;
        }

        .search-input {
          width: 100%;
          padding: 0.75rem 1rem 0.75rem 2.5rem;
          border: 1px solid var(--border-color);
          border-radius: 0.5rem;
          font-size: 0.95rem;
          color: var(--text-dark);
          transition: border-color 0.2s, box-shadow 0.2s;
        }

        .search-input:focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
          outline: none;
        }

        .search-icon {
          position: absolute;
          left: 0.8rem;
          top: 50%;
          transform: translateY(-50%);
          color: var(--text-light);
        }

        .filter-select {
          padding: 0.75rem 1rem;
          border: 1px solid var(--border-color);
          border-radius: 0.5rem;
          background-color: white;
          font-size: 0.95rem;
          color: var(--text-dark);
          cursor: pointer;
          appearance: none;
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd'%3E%3C/path%3E%3C/svg%3E");
          background-repeat: no-repeat;
          background-position: right 0.75rem center;
          background-size: 1.25rem;
          transition: border-color 0.2s;
        }

        .filter-select:focus {
          border-color: var(--primary-color);
          outline: none;
        }

        .clear-filters-button {
            background: none;
            border: none;
            color: var(--red-danger);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.4rem;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            transition: background 0.2s;
        }
        .clear-filters-button:hover {
            background: var(--red-light);
            color: var(--red-dark);
        }

        /* Table */
        .table-card {
          background: var(--card-bg);
          border-radius: 0.75rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
          overflow: hidden;
          margin-bottom: 2.5rem;
        }

        .table-wrapper {
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
        }

        table {
          width: 100%;
          border-collapse: collapse;
          min-width: 900px; /* Ensure table doesn't get too small horizontally */
        }

        th {
          text-align: left;
          padding: 1rem 1.5rem;
          font-size: 0.85rem;
          font-weight: 600;
          color: var(--text-light);
          text-transform: uppercase;
          background: var(--bg-light);
          border-bottom: 1px solid var(--border-color);
        }

        td {
          padding: 1rem 1.5rem;
          font-size: 0.95rem;
          color: var(--text-medium);
          border-top: 1px solid var(--border-color);
        }

        tbody tr:last-child td {
          border-bottom: none;
        }

        tbody tr:hover {
          background-color: var(--bg-light);
        }

        .text-center-message {
            text-align: center;
            padding: 2rem 0;
            color: var(--text-light);
            font-style: italic;
        }

        .status-badge, .plan-badge {
          display: inline-block;
          padding: 0.3rem 0.7rem;
          border-radius: 0.5rem;
          font-size: 0.75rem;
          font-weight: 700;
          text-transform: capitalize;
          white-space: nowrap;
        }

        .status-active {
          background: var(--green-light);
          color: var(--green-success);
        }

        .status-pending {
          background: var(--yellow-light);
          color: var(--yellow-warning);
        }

        .status-inactive {
          background: var(--red-light);
          color: var(--red-danger);
        }

        .plan-enterprise {
          background: var(--primary-light);
          color: var(--primary-color);
        }
        .plan-standard {
          background: var(--blue-light);
          color: var(--blue-info);
        }
        .plan-basic {
          background: var(--bg-light);
          color: var(--text-medium);
        }

        .action-buttons-cell {
          white-space: nowrap;
        }

        .action-button {
          background: var(--primary-light);
          color: var(--primary-color);
          padding: 0.6rem;
          border-radius: 0.375rem;
          border: none;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          transition: background 0.2s, transform 0.1s;
          margin-right: 0.5rem;
        }

        .action-button:last-child {
            margin-right: 0;
        }

        .action-button:hover {
          background: var(--primary-color);
          color: white;
          transform: translateY(-1px);
        }
        .action-button.view {
            background: var(--blue-light);
            color: var(--blue-info);
        }
        .action-button.view:hover {
            background: var(--blue-info);
            color: white;
        }
        .action-button.deactivate { /* For "trash" or deactivate icon */
          background: var(--red-light);
          color: var(--red-danger);
        }
        .action-button.deactivate:hover {
          background: var(--red-danger);
          color: white;
        }
        .action-button.activate { /* For "check" or activate icon */
          background: var(--green-light);
          color: var(--green-success);
        }
        .action-button.activate:hover {
          background: var(--green-success);
          color: white;
        }

        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
          animation: fadeIn 0.2s ease-out;
        }

        .modal-content {
          background: white;
          padding: 2rem;
          border-radius: 0.75rem;
          box-shadow: var(--shadow-lg);
          max-width: 600px; /* Increased max-width for forms */
          width: 90%;
          position: relative;
          animation: slideIn 0.3s ease-out;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideIn {
          from { transform: translateY(-30px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
          border-bottom: 1px solid var(--border-color);
          padding-bottom: 1rem;
        }

        .modal-header h2 {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--text-dark);
          margin: 0;
        }

        .modal-close-button {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: var(--text-light);
          transition: color 0.2s;
        }
        .modal-close-button:hover {
          color: var(--red-danger);
        }

        /* Form Grid for better input arrangement */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.2rem 1.5rem;
        }
        .form-grid .form-group:nth-child(1),
        .form-grid .form-group:nth-child(2) { /* Span full width for name and email */
            grid-column: span 2;
        }


        .form-group {
          margin-bottom: 0; /* Remove default margin as grid handles spacing */
        }

        .form-group label {
          display: block;
          margin-bottom: 0.5rem;
          font-size: 0.9rem;
          font-weight: 600;
          color: var(--text-medium);
        }

        .form-group input,
        .form-group select {
          width: 100%;
          padding: 0.8rem 1rem;
          border: 1px solid var(--border-color);
          border-radius: 0.5rem;
          font-size: 0.95rem;
          color: var(--text-dark);
          transition: border-color 0.2s, box-shadow 0.2s;
          background-color: white;
        }

        .form-group input:focus,
        .form-group select:focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
          outline: none;
        }

        .form-group input.input-error,
        .form-group select.input-error {
            border-color: var(--red-danger);
        }

        .error-message {
            color: var(--red-danger);
            font-size: 0.8rem;
            margin-top: 0.4rem;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            grid-column: span 2; /* Make checkbox span full width */
            margin-top: 0.5rem; /* Small margin for visual separation */
        }

        .checkbox-group input[type="checkbox"] {
            width: auto; /* Override 100% width */
            margin-right: 0.6rem;
            transform: scale(1.2); /* Slightly larger checkbox */
            cursor: pointer;
        }

        .checkbox-group label {
            margin-bottom: 0;
            cursor: pointer;
        }

        .modal-actions {
          display: flex;
          justify-content: flex-end;
          gap: 0.75rem;
          margin-top: 2rem;
          padding-top: 1rem;
          border-top: 1px solid var(--border-color);
        }

        .button-primary, .button-secondary, .button-danger, .button-success {
          padding: 0.8rem 1.5rem;
          border-radius: 0.5rem;
          border: none;
          font-weight: 600;
          font-size: 0.95rem;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          transition: all 0.2s ease-in-out;
        }

        .button-primary {
          background: var(--primary-color);
          color: white;
          box-shadow: var(--shadow-sm);
        }
        .button-primary:hover {
          background: var(--primary-dark);
          transform: translateY(-1px);
        }

        .button-secondary {
          background: var(--bg-light);
          color: var(--text-medium);
          border: 1px solid var(--border-color);
        }
        .button-secondary:hover {
          background: var(--border-color);
          color: var(--text-dark);
        }

        .button-danger {
          background: var(--red-danger);
          color: white;
          box-shadow: var(--shadow-sm);
        }
        .button-danger:hover {
          background: #dc2626;
          transform: translateY(-1px);
        }
        .button-success {
            background: var(--green-success);
            color: white;
            box-shadow: var(--shadow-sm);
        }
        .button-success:hover {
            background: #059669; /* Darker green */
            transform: translateY(-1px);
        }


        .modal-message {
            font-size: 1rem;
            color: var(--text-medium);
            line-height: 1.5;
            margin-bottom: 1.5rem;
        }

        /* Tenant Details Modal specific styles */
        .details-grid {
            display: grid;
            grid-template-columns: auto 1fr; /* Label on left, value on right */
            gap: 1rem 1.5rem;
            padding: 0.5rem;
            border-bottom: 1px solid var(--border-color); /* Separator below details */
            margin-bottom: 1.5rem;
        }
        .detail-item {
            display: contents; /* Allows children to participate in the grid layout */
        }
        .detail-label {
            font-weight: 600;
            color: var(--text-dark);
            text-align: right; /* Align labels to the right */
        }
        .detail-value {
            color: var(--text-medium);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }


        /* Responsive adjustments */
        @media (max-width: 1024px) {
            .admin-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
                padding: 1.2rem 1.5rem;
            }
            .admin-title {
                font-size: 1.75rem;
            }
            .filter-bar {
                flex-direction: column;
                align-items: stretch;
                gap: 0.8rem;
            }
            .search-input-wrapper {
                min-width: unset;
                width: 100%;
            }
            .filter-select {
                width: 100%;
            }
        }

        @media (max-width: 768px) {
            .admin-page-container {
                padding: 1.5rem 1rem;
            }
            .admin-header, .filter-bar, .table-card {
                padding: 1rem;
            }
            .admin-title {
                font-size: 1.5rem;
            }
            th, td {
                padding: 0.8rem 1rem;
                font-size: 0.9rem;
            }
            .action-button {
                padding: 0.5rem;
                margin-right: 0.4rem;
            }
            .modal-content {
                padding: 1.5rem;
                max-width: 95%; /* Adjust modal width for small screens */
            }
            .modal-header h2 {
                font-size: 1.3rem;
            }
            .modal-actions {
                flex-direction: column;
                gap: 0.5rem;
            }
            .button-primary, .button-secondary, .button-danger, .button-success {
                width: 100%;
                justify-content: center;
            }
            .form-grid {
                grid-template-columns: 1fr; /* Stack form groups on small screens */
                gap: 1rem;
            }
            .form-grid .form-group:nth-child(1),
            .form-grid .form-group:nth-child(2) { /* Reset span for small screens */
                grid-column: span 1;
            }
            .checkbox-group {
                grid-column: span 1;
            }
            .details-grid {
                grid-template-columns: 1fr; /* Stack details on small screens */
                text-align: left;
            }
            .detail-label {
                text-align: left;
            }
        }
      `}</style>
    </div>
  );
};

export default Tenants;