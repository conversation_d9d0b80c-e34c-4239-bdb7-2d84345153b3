import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  LayoutDashboard,
  FilePlus,
  User,
  Edit,
  Library,
  BarChart2,
  HelpCircle,
  LogOut
} from 'lucide-react';

type MenuItem = {
  key: string;
  label: string;
  to: string;
  icon: React.ReactNode;
};

const InstructorSidebar = () => {
  const menuItems: MenuItem[] = [
    {
      key: 'dashboard',
      label: 'Dashboard',
      to: '/instructor/dashboard',
      icon: <LayoutDashboard size={18} />
    },
    {
      key: 'create',
      label: 'Create Exam',
      to: '/instructor/exam/new',
      icon: <FilePlus size={18} />
    },
    {
      key: 'edit',
      label: 'Edit Exam',
      to: '/instructor/exam/123/edit',
      icon: <Edit size={18} />
    },
    {
      key: 'questions',
      label: 'Question Bank',
      to: '/instructor/questions',
      icon: <Library size={18} />
    },
    {
      key: 'analytics',
      label: 'Analytics',
      to: '/instructor/exam/123/analytics',
      icon: <BarChart2 size={18} />
    },
    {
      key: 'profile',
      label: 'Profile',
      to: '/instructor/profile',
      icon: <User size={18} />
    },
    {
      key: 'help',
      label: 'help',
      to: '/instructor/help',
      icon: <HelpCircle size={18} />
    },
    {
      key: 'logout',
      label: 'Logout',
      to: '/login',
      icon: <LogOut size={18} />
    },
  ];

  return (
    <div className="sidebar-container">
      <div className="sidebar-header">
        <h1 className="sidebar-title">Instructor Panel</h1>
      </div>
      <nav className="sidebar-nav">
        {menuItems.map(item => (
          <NavLink
            key={item.key}
            to={item.to}
            className={({ isActive }) =>
              `sidebar-link ${isActive ? 'active' : ''}`
            }
          >
            <span className="sidebar-icon">{item.icon}</span>
            <span className="sidebar-label">{item.label}</span>
          </NavLink>
        ))}
      </nav>

      <style>{`
        .sidebar-container {
          width: 16rem;
          background-color: #1e293b;
          color: white;
          min-height: 100vh;
          display: flex;
          flex-direction: column;
        }
        .sidebar-header {
          padding: 1.5rem;
          border-bottom: 1px solid #334155;
        }
        .sidebar-title {
          font-size: 1.25rem;
          font-weight: 700;
        }
        .sidebar-nav {
          flex: 1;
          padding: 0.5rem 0;
          display: flex;
          flex-direction: column;
        }
        .sidebar-link {
          display: flex;
          align-items: center;
          padding: 0.75rem 1.5rem;
          margin: 0.25rem 0.5rem;
          border-radius: 0.375rem;
          color: #e2e8f0;
          text-decoration: none;
          transition: all 0.2s ease;
        }
        .sidebar-link:hover {
          background-color: #334155;
          color: white;
        }
        .sidebar-link.active {
          background-color: #4f46e5;
          color: white;
        }
        .sidebar-icon {
          margin-right: 0.75rem;
        }
        .sidebar-label {
          font-size: 0.875rem;
          font-weight: 500;
        }
        .sidebar-link[href="/login"] {
          margin-top: auto;
          margin-bottom: 1rem;
          color: #f87171;
        }
        .sidebar-link[href="/login"]:hover {
          background-color: #7f1d1d;
          color: #fecaca;
        }
        .sidebar-link[href="/login"].active {
          background-color: #7f1d1d;
          color: #fecaca;
        }
      `}</style>
    </div>
  );
};

export default InstructorSidebar;
