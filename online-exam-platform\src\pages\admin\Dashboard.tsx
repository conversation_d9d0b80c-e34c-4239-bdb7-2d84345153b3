import { useState, useEffect } from 'react';
import { FiUsers, FiActivity, FiAlertTriangle, FiClock, FiServer, FiBarChart2, FiShield, FiList } from 'react-icons/fi';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const Dashboard = () => {
  const [stats, setStats] = useState({
    users: 0,
    exams: 0,
    flags: 0,
    uptime: '0%',
    activeUsers: 0,
    systemHealth: 'Loading...',
    storageUsed: '0GB/0GB',
    responseTime: '0ms'
  });

  type AuditLog = {
    id: number;
    action: string;
    user: string;
    timestamp: string;
    status: string;
  };
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');

  // Simulate data fetching
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Mock data
      setStats({
        users: 1243,
        exams: 58,
        flags: 14,
        uptime: '99.98%',
        activeUsers: 327,
        systemHealth: 'Excellent',
        storageUsed: '24GB/100GB',
        responseTime: '42ms'
      });

      setAuditLogs([
        { id: 1, action: 'User login', user: '<EMAIL>', timestamp: '2023-06-15T14:32:45Z', status: 'success' },
        { id: 2, action: 'Exam created', user: '<EMAIL>', timestamp: '2023-06-15T13:45:12Z', status: 'success' },
        { id: 3, action: 'Failed login attempt', user: '<EMAIL>', timestamp: '2023-06-15T12:18:33Z', status: 'failed' },
        { id: 4, action: 'User registered', user: '<EMAIL>', timestamp: '2023-06-15T10:05:21Z', status: 'success' },
        { id: 5, action: 'System backup', user: 'system', timestamp: '2023-06-15T04:00:00Z', status: 'success' },
      ]);

      setLoading(false);
    };

    fetchData();
  }, []);

  // Chart data
  const userGrowthData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'New Users',
        data: [120, 190, 170, 220, 280, 350],
        borderColor: 'rgba(16, 185, 129, 1)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.3,
        fill: true
      }
    ]
  };

  const systemUsageData = {
    labels: ['CPU', 'Memory', 'Storage', 'Network'],
    datasets: [
      {
        label: 'Usage %',
        data: [45, 68, 24, 32],
        backgroundColor: [
          'rgba(16, 185, 129, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(139, 92, 246, 0.8)'
        ],
        borderColor: [
          'rgba(16, 185, 129, 1)',
          'rgba(59, 130, 246, 1)',
          'rgba(245, 158, 11, 1)',
          'rgba(139, 92, 246, 1)'
        ],
        borderWidth: 1
      }
    ]
  };

interface StatusColorMap {
    [key: string]: string;
}

const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
        case 'success': return 'status-success';
        case 'failed': return 'status-failed';
        default: return 'status-default';
    }
};

  return (
    <div className="dashboard-container">
      <div className="dashboard-content">
        <div className="dashboard-header">
          <h1>System Dashboard</h1>
          <div className="time-range-selector">
            <button 
              onClick={() => setTimeRange('24h')} 
              className={timeRange === '24h' ? 'active' : ''}
            >
              24h
            </button>
            <button 
              onClick={() => setTimeRange('7d')} 
              className={timeRange === '7d' ? 'active' : ''}
            >
              7d
            </button>
            <button 
              onClick={() => setTimeRange('30d')} 
              className={timeRange === '30d' ? 'active' : ''}
            >
              30d
            </button>
          </div>
        </div>

        {loading ? (
          <div className="loading-spinner">
            <div className="spinner"></div>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-icon">
                  <FiUsers size={24} />
                </div>
                <div className="stat-info">
                  <p className="stat-label">Total Users</p>
                  <p className="stat-value">{stats.users}</p>
                </div>
                <div className="stat-progress">
                  <div className="progress-bar" style={{ width: '75%' }}></div>
                  <p className="progress-text">+12.5% from last month</p>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon blue">
                  <FiActivity size={24} />
                </div>
                <div className="stat-info">
                  <p className="stat-label">Active Users</p>
                  <p className="stat-value">{stats.activeUsers}</p>
                </div>
                <div className="stat-progress">
                  <div className="progress-bar blue" style={{ width: '42%' }}></div>
                  <p className="progress-text">+8.3% from yesterday</p>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon yellow">
                  <FiAlertTriangle size={24} />
                </div>
                <div className="stat-info">
                  <p className="stat-label">Flags</p>
                  <p className="stat-value">{stats.flags}</p>
                </div>
                <div className="stat-progress">
                  <div className="progress-bar yellow" style={{ width: '18%' }}></div>
                  <p className="progress-text">-3 from last week</p>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon purple">
                  <FiServer size={24} />
                </div>
                <div className="stat-info">
                  <p className="stat-label">System Health</p>
                  <p className="stat-value">{stats.systemHealth}</p>
                </div>
                <div className="stat-progress">
                  <div className="progress-bar purple" style={{ width: '92%' }}></div>
                  <p className="progress-text">Uptime: {stats.uptime}</p>
                </div>
              </div>
            </div>

            {/* Charts Row */}
            <div className="charts-grid">
              <div className="chart-card">
                <div className="chart-header">
                  <h2>User Growth</h2>
                  <p>Last 6 months</p>
                </div>
                <div className="chart-container">
                  <Line 
                    data={userGrowthData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'top',
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true
                        }
                      }
                    }}
                  />
                </div>
              </div>

              <div className="chart-card">
                <div className="chart-header">
                  <h2>System Usage</h2>
                  <p>Current allocation</p>
                </div>
                <div className="chart-container">
                  <Bar
                    data={systemUsageData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'top',
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          max: 100
                        }
                      }
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Bottom Row */}
            <div className="bottom-grid">
              <div className="activity-card">
                <div className="activity-header">
                  <h2>Recent Activity</h2>
                  <button className="view-all">View All</button>
                </div>
                <div className="activity-table">
                  <table>
                    <thead>
                      <tr>
                        <th>Action</th>
                        <th>User</th>
                        <th>Time</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {auditLogs.map((log) => (
                        <tr key={log.id}>
                          <td>{log.action}</td>
                          <td>{log.user}</td>
                          <td>{new Date(log.timestamp).toLocaleString()}</td>
                          <td>
                            <span className={`status-badge ${getStatusColor(log.status)}`}>
                              {log.status}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              <div className="quick-stats-card">
                <div className="quick-stats-header">
                  <h2>Quick Stats</h2>
                  <FiBarChart2 />
                </div>
                <div className="quick-stats-content">
                  <div className="quick-stat">
                    <p className="stat-label">Storage Used</p>
                    <p className="stat-value">{stats.storageUsed}</p>
                    <div className="stat-progress">
                      <div className="progress-bar blue" style={{ width: '24%' }}></div>
                    </div>
                  </div>
                  <div className="quick-stat">
                    <p className="stat-label">Avg. Response Time</p>
                    <p className="stat-value">{stats.responseTime}</p>
                    <div className="stat-progress">
                      <div className="progress-bar" style={{ width: '85%' }}></div>
                    </div>
                  </div>
                  <div className="quick-stat">
                    <p className="stat-label">Exams Created</p>
                    <p className="stat-value">{stats.exams}</p>
                    <div className="stat-progress">
                      <div className="progress-bar purple" style={{ width: '65%' }}></div>
                    </div>
                  </div>
                  <div className="report-button-container">
                    <button className="report-button">
                      <FiShield />
                      Generate System Report
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      <style>{`
        .dashboard-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          padding: 2rem;
        }

        .dashboard-content {
          max-width: 1200px;
          margin: 0 auto;
        }

        .dashboard-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
        }

        .dashboard-header h1 {
          font-size: 1.75rem;
          font-weight: 700;
          color: #1f2937;
          margin: 0;
        }

        .time-range-selector {
          display: flex;
          gap: 0.5rem;
        }

        .time-range-selector button {
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
          border: none;
          background: white;
          color: #4b5563;
          font-size: 0.875rem;
          cursor: pointer;
          transition: all 0.2s;
        }

        .time-range-selector button.active {
          background: #4f46e5;
          color: white;
        }

        .loading-spinner {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 300px;
        }

        .spinner {
          width: 3rem;
          height: 3rem;
          border: 4px solid rgba(79, 70, 229, 0.1);
          border-top-color: #4f46e5;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          to { transform: rotate(360deg); }
        }

        /* Stats Grid */
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 1.5rem;
          margin-bottom: 2rem;
        }

        .stat-card {
          background: white;
          border-radius: 0.75rem;
          padding: 1.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .stat-icon {
          display: inline-flex;
          padding: 0.75rem;
          border-radius: 50%;
          background: #ecfdf5;
          color: #10b981;
        }

        .stat-icon.blue {
          background: #eff6ff;
          color: #3b82f6;
        }

        .stat-icon.yellow {
          background: #fef3c7;
          color: #f59e0b;
        }

        .stat-icon.purple {
          background: #f5f3ff;
          color: #8b5cf6;
        }

        .stat-info {
          margin-top: 1rem;
        }

        .stat-label {
          font-size: 0.875rem;
          color: #6b7280;
          margin: 0;
        }

        .stat-value {
          font-size: 1.5rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0.25rem 0 0 0;
        }

        .stat-progress {
          margin-top: 1.5rem;
        }

        .progress-bar {
          height: 0.5rem;
          background: #10b981;
          border-radius: 0.25rem;
        }

        .progress-bar.blue {
          background: #3b82f6;
        }

        .progress-bar.yellow {
          background: #f59e0b;
        }

        .progress-bar.purple {
          background: #8b5cf6;
        }

        .progress-text {
          font-size: 0.75rem;
          color: #9ca3af;
          margin: 0.25rem 0 0 0;
        }

        /* Charts Grid */
        .charts-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
          gap: 1.5rem;
          margin-bottom: 2rem;
        }

        .chart-card {
          background: white;
          border-radius: 0.75rem;
          padding: 1.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .chart-header h2 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
        }

        .chart-header p {
          font-size: 0.875rem;
          color: #6b7280;
          margin: 0;
        }

        .chart-container {
          height: 300px;
        }

        /* Bottom Grid */
        .bottom-grid {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: 1.5rem;
        }

        .activity-card, .quick-stats-card {
          background: white;
          border-radius: 0.75rem;
          padding: 1.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .activity-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .activity-header h2 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
        }

        .view-all {
          background: none;
          border: none;
          color: #10b981;
          font-size: 0.875rem;
          cursor: pointer;
          padding: 0;
        }

        .activity-table {
          overflow-x: auto;
        }

        table {
          width: 100%;
          border-collapse: collapse;
        }

        th {
          text-align: left;
          padding: 0.75rem 1rem;
          font-size: 0.75rem;
          font-weight: 500;
          color: #6b7280;
          text-transform: uppercase;
          background: #f9fafb;
        }

        td {
          padding: 1rem;
          font-size: 0.875rem;
          border-top: 1px solid #e5e7eb;
        }

        .status-badge {
          display: inline-block;
          padding: 0.25rem 0.5rem;
          border-radius: 0.375rem;
          font-size: 0.75rem;
          font-weight: 600;
        }

        .status-success {
          background: #ecfdf5;
          color: #059669;
        }

        .status-failed {
          background: #fee2e2;
          color: #dc2626;
        }

        .status-default {
          background: #f3f4f6;
          color: #4b5563;
        }

        /* Quick Stats */
        .quick-stats-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .quick-stats-header h2 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
        }

        .quick-stats-header svg {
          color: #9ca3af;
        }

        .quick-stats-content {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }

        .quick-stat {
          margin-bottom: 0.5rem;
        }

        .quick-stat .stat-label {
          font-size: 0.875rem;
          color: #6b7280;
          margin: 0 0 0.25rem 0;
        }

        .quick-stat .stat-value {
          font-size: 1.125rem;
          margin: 0 0 0.5rem 0;
        }

        .report-button-container {
          padding-top: 1rem;
          border-top: 1px solid #e5e7eb;
          margin-top: 0.5rem;
        }

        .report-button {
          width: 100%;
          padding: 0.75rem;
          background: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          font-size: 0.875rem;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          transition: background 0.2s;
        }

        .report-button:hover {
          background: #4338ca;
        }

        .report-button svg {
          width: 1rem;
          height: 1rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .bottom-grid {
            grid-template-columns: 1fr;
          }
          
          .charts-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default Dashboard;