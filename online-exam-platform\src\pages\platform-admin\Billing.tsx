import React, { useState, useEffect } from 'react';
import { FiSearch, FiEdit, FiDollarSign, FiZap, FiFileText, FiX, FiCheck, FiBarChart2, Fi<PERSON><PERSON>tTriangle, FiList } from 'react-icons/fi';
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';

// Register ChartJS components for global use
ChartJS.register(ArcElement, Tooltip, Legend);

// Define types for better type safety
interface Subscription {
  id: number;
  tenant: string;
  plan: 'Free' | 'Basic' | 'Standard' | 'Enterprise'; // Added more plan types
  amount: number;
  nextBill: string;
  status: 'Active' | 'Overdue' | 'Cancelled' | 'Trial'; // Added more statuses
}

interface Payment {
  id: number;
  subscriptionId: number;
  tenant: string;
  amount: number;
  date: string;
  status: 'Paid' | 'Failed' | 'Refunded';
  invoiceUrl: string;
}

const Billing = () => {
  // Mock Data
  const initialSubscriptions: Subscription[] = [
    { id: 101, tenant: 'University A', plan: 'Enterprise', amount: 500, nextBill: '2025-06-15', status: 'Active' },
    { id: 102, tenant: 'University B', plan: 'Standard', amount: 200, nextBill: '2025-07-01', status: 'Active' },
    { id: 103, tenant: 'Global School', plan: 'Enterprise', amount: 500, nextBill: '2025-05-20', status: 'Overdue' },
    { id: 104, tenant: 'Tech Academy', plan: 'Basic', amount: 50, nextBill: '2025-06-10', status: 'Active' },
    { id: 105, tenant: 'Open Learning', plan: 'Free', amount: 0, nextBill: 'N/A', status: 'Trial' },
    { id: 106, tenant: 'Community College', plan: 'Standard', amount: 200, nextBill: '2025-04-01', status: 'Cancelled' },
  ];

  const initialPayments: Payment[] = [
    { id: 1, subscriptionId: 101, tenant: 'University A', amount: 500, date: '2025-05-15', status: 'Paid', invoiceUrl: '#' },
    { id: 2, subscriptionId: 102, tenant: 'University B', amount: 200, date: '2025-06-01', status: 'Paid', invoiceUrl: '#' },
    { id: 3, subscriptionId: 103, tenant: 'Global School', amount: 500, date: '2025-04-20', status: 'Failed', invoiceUrl: '#' },
    { id: 4, subscriptionId: 104, tenant: 'Tech Academy', amount: 50, date: '2025-05-10', status: 'Paid', invoiceUrl: '#' },
    { id: 5, subscriptionId: 101, tenant: 'University A', amount: 500, date: '2025-04-15', status: 'Paid', invoiceUrl: '#' },
  ];

  // State
  const [subscriptions, setSubscriptions] = useState<Subscription[]>(initialSubscriptions);
  const [payments, setPayments] = useState<Payment[]>(initialPayments);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [filterPlan, setFilterPlan] = useState('');

  // Modals state
  const [isManageSubModalOpen, setIsManageSubModalOpen] = useState(false);
  const [isViewInvoicesModalOpen, setIsViewInvoicesModalOpen] = useState(false);
  const [isSendReminderModalOpen, setIsSendReminderModalOpen] = useState(false);
  const [currentSubscription, setCurrentSubscription] = useState<Subscription | null>(null);

  // Form data for managing subscription
  const [formData, setFormData] = useState<Partial<Subscription>>({});
  const [formErrors, setFormErrors] = useState<any>({});

  // --- Helper Functions ---
  const getStatusBadgeClass = (status: Subscription['status'] | Payment['status']) => {
    switch (status) {
      case 'Active': return 'status-active';
      case 'Paid': return 'status-active';
      case 'Overdue': return 'status-overdue';
      case 'Failed': return 'status-overdue'; // Using overdue style for failed payments
      case 'Cancelled': return 'status-cancelled';
      case 'Trial': return 'status-trial';
      case 'Refunded': return 'status-refunded';
      default: return 'status-default';
    }
  };

  const validateForm = () => {
    const errors: any = {};
    if (formData.amount !== undefined && (isNaN(formData.amount) || formData.amount <= 0)) {
      errors.amount = 'Amount must be a positive number';
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const resetFormData = () => {
    setFormData({});
    setFormErrors({});
  };

  // --- CRUD/Action Functions ---

  // Open Manage Subscription Modal
  const handleManageSubscriptionClick = (sub: Subscription) => {
    setCurrentSubscription(sub);
    setFormData({
      plan: sub.plan,
      amount: sub.amount,
      nextBill: sub.nextBill === 'N/A' ? '' : sub.nextBill, // Handle 'N/A' for Free plans
      status: sub.status
    });
    setIsManageSubModalOpen(true);
  };

  // Save Subscription Changes
  const handleSaveSubscription = () => {
    if (!currentSubscription || !validateForm()) return;

    setSubscriptions(subscriptions.map(sub =>
      sub.id === currentSubscription.id
        ? {
            ...sub,
            plan: formData.plan || sub.plan,
            amount: formData.amount !== undefined ? formData.amount : sub.amount,
            nextBill: formData.nextBill || sub.nextBill,
            status: formData.status || sub.status
          }
        : sub
    ));
    setIsManageSubModalOpen(false);
    setCurrentSubscription(null);
    resetFormData();
  };

  // View Invoices
  const handleViewInvoicesClick = (sub: Subscription) => {
    setCurrentSubscription(sub);
    setIsViewInvoicesModalOpen(true);
  };

  // Send Reminder
  const handleSendReminderClick = (sub: Subscription) => {
    setCurrentSubscription(sub);
    setIsSendReminderModalOpen(true);
  };

  const confirmSendReminder = () => {
    if (currentSubscription) {
      console.log(`Sending reminder to ${currentSubscription.tenant} for subscription ${currentSubscription.id}`);
      // In a real app, integrate with an email service here
      alert(`Reminder sent to ${currentSubscription.tenant}! (Simulated)`);
    }
    setIsSendReminderModalOpen(false);
    setCurrentSubscription(null);
  };

  // --- Filtering ---
  const filteredSubscriptions = subscriptions.filter(sub => {
    const matchesSearch = sub.tenant.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          String(sub.id).includes(searchTerm);
    const matchesStatus = filterStatus ? sub.status === filterStatus : true;
    const matchesPlan = filterPlan ? sub.plan === filterPlan : true;
    return matchesSearch && matchesStatus && matchesPlan;
  });

  // --- Chart Data ---
  const planDistribution = subscriptions.reduce((acc, sub) => {
    acc[sub.plan] = (acc[sub.plan] || 0) + 1;
    return acc;
  }, {} as Record<Subscription['plan'], number>);

  const pieChartData = {
    labels: Object.keys(planDistribution),
    datasets: [
      {
        data: Object.values(planDistribution),
        backgroundColor: [
          '#4f46e5', // Primary - Enterprise
          '#10b981', // Green - Standard
          '#3b82f6', // Blue - Basic
          '#9ca3af', // Gray - Free
          '#f59e0b', // Yellow - Trial
        ],
        borderColor: '#ffffff',
        borderWidth: 2,
      },
    ],
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const, // Position legend on the right
        labels: {
          font: {
            family: 'Inter',
            size: 14,
          },
          color: '#4b5563',
          boxWidth: 20, // Adjust legend box size
          padding: 15, // Padding between legend items
        },
      },
      tooltip: {
        titleFont: { family: 'Inter', size: 14, weight: 700 },
        bodyFont: { family: 'Inter', size: 12 },
        backgroundColor: 'rgba(0,0,0,0.7)',
        padding: 10,
        cornerRadius: 6,
        callbacks: {
          label: function(context: any) {
            let label = context.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed !== null) {
              label += `${context.parsed} subscriptions`;
            }
            return label;
          }
        }
      },
    },
  };


  // --- Rendered JSX ---
  return (
    <div className="admin-page-container">
      <div className="admin-page-wrapper">
        <div className="admin-header">
          <h1 className="admin-title">Billing & Subscriptions</h1>
          <div className="header-actions">
            <button className="button-secondary">
              <FiDollarSign size={18} /> Generate Invoice
            </button>
            <button className="button-primary">
              <FiZap size={18} /> Quick Bill Run
            </button>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="billing-overview-grid">
            <div className="overview-card">
                <FiDollarSign size={32} className="card-icon primary" />
                <p className="card-label">Total Monthly Recurring Revenue</p>
                <p className="card-value">$ {subscriptions.filter(s => s.status === 'Active').reduce((sum, s) => sum + s.amount, 0).toLocaleString()}</p>
            </div>
            <div className="overview-card">
                <FiFileText size={32} className="card-icon green" />
                <p className="card-label">Active Subscriptions</p>
                <p className="card-value">{subscriptions.filter(s => s.status === 'Active').length}</p>
            </div>
            <div className="overview-card">
                <FiAlertTriangle size={32} className="card-icon red" />
                <p className="card-label">Overdue Payments</p>
                <p className="card-value">{subscriptions.filter(s => s.status === 'Overdue').length}</p>
            </div>
        </div>

        {/* Subscription Distribution Chart */}
        <div className="chart-card">
            <div className="chart-header">
                <h2>Subscription Plan Distribution</h2>
                <p className="chart-subtitle">Breakdown of tenants by plan type</p>
            </div>
            <div className="chart-container-small">
                <Pie data={pieChartData} options={pieChartOptions} />
            </div>
        </div>

        {/* Search and Filter Bar - Subscriptions */}
        <div className="filter-bar">
          <div className="search-input-wrapper">
            <FiSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search subscriptions by tenant or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          <select
            value={filterPlan}
            onChange={(e) => setFilterPlan(e.target.value)}
            className="filter-select"
            aria-label="Filter by plan"
          >
            <option value="">All Plans</option>
            <option value="Enterprise">Enterprise</option>
            <option value="Standard">Standard</option>
            <option value="Basic">Basic</option>
            <option value="Free">Free</option>
          </select>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="filter-select"
            aria-label="Filter by status"
          >
            <option value="">All Statuses</option>
            <option value="Active">Active</option>
            <option value="Overdue">Overdue</option>
            <option value="Cancelled">Cancelled</option>
            <option value="Trial">Trial</option>
          </select>
          {(searchTerm || filterStatus || filterPlan) && (
            <button className="clear-filters-button" onClick={() => {
              setSearchTerm('');
              setFilterStatus('');
              setFilterPlan('');
            }}>
              Clear Filters <FiX size={16} />
            </button>
          )}
        </div>

        {/* Subscriptions Table */}
        <div className="table-card">
          <div className="table-header">
            <h2>Tenant Subscriptions</h2>
            <button className="view-all-button">
              <FiList size={18} /> Full List
            </button>
          </div>
          <div className="table-wrapper">
            <table>
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Tenant</th>
                  <th>Plan</th>
                  <th>Amount ($)</th>
                  <th>Next Bill</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredSubscriptions.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="text-center-message">No subscriptions found matching your criteria.</td>
                  </tr>
                ) : (
                  filteredSubscriptions.map((sub) => (
                    <tr key={sub.id}>
                      <td>{sub.id}</td>
                      <td>{sub.tenant}</td>
                      <td>{sub.plan}</td>
                      <td>${sub.amount.toLocaleString()}</td>
                      <td>{sub.nextBill}</td>
                      <td>
                        <span className={`status-badge ${getStatusBadgeClass(sub.status)}`}>
                          {sub.status}
                        </span>
                      </td>
                      <td className="action-buttons-cell">
                        <button
                          className="action-button primary"
                          onClick={() => handleManageSubscriptionClick(sub)}
                          title="Manage Subscription"
                        >
                          <FiEdit size={16} />
                        </button>
                        <button
                          className="action-button info"
                          onClick={() => handleViewInvoicesClick(sub)}
                          title="View Invoices"
                        >
                          <FiFileText size={16} />
                        </button>
                        {sub.status === 'Overdue' && (
                          <button
                            className="action-button warning"
                            onClick={() => handleSendReminderClick(sub)}
                            title="Send Reminder"
                          >
                            <FiZap size={16} />
                          </button>
                        )}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Recent Payments Section */}
        <div className="table-card">
          <div className="table-header">
            <h2>Recent Payments</h2>
            <button className="view-all-button">
              <FiList size={18} /> All Payments
            </button>
          </div>
          <div className="table-wrapper">
            <table>
              <thead>
                <tr>
                  <th>Payment ID</th>
                  <th>Tenant</th>
                  <th>Subscription ID</th>
                  <th>Amount ($)</th>
                  <th>Date</th>
                  <th>Status</th>
                  <th>Invoice</th>
                </tr>
              </thead>
              <tbody>
                {payments.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="text-center-message">No recent payments found.</td>
                  </tr>
                ) : (
                  payments.slice(0, 5).map((payment) => ( // Show only 5 recent payments
                    <tr key={payment.id}>
                      <td>{payment.id}</td>
                      <td>{payment.tenant}</td>
                      <td>{payment.subscriptionId}</td>
                      <td>${payment.amount.toLocaleString()}</td>
                      <td>{payment.date}</td>
                      <td>
                        <span className={`status-badge ${getStatusBadgeClass(payment.status)}`}>
                          {payment.status}
                        </span>
                      </td>
                      <td>
                        <a href={payment.invoiceUrl} target="_blank" rel="noopener noreferrer" className="text-link">View Invoice</a>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Modals */}

        {/* Manage Subscription Modal */}
        {isManageSubModalOpen && currentSubscription && (
          <Modal title={`Manage Subscription: ${currentSubscription.tenant}`} onClose={() => setIsManageSubModalOpen(false)}>
            <form onSubmit={(e) => { e.preventDefault(); handleSaveSubscription(); }}>
              <div className="form-group">
                <label>Tenant:</label>
                <input type="text" value={currentSubscription.tenant} disabled className="input-disabled" title="Tenant" />
              </div>
              <div className="form-group">
                <label>Current Plan:</label>
                <input type="text" value={currentSubscription.plan} disabled className="input-disabled" title="Current Plan" />
              </div>
              <div className="form-group">
                <label>Change Plan to:</label>
                <select
                  value={formData.plan || ''}
                  onChange={(e) => setFormData({ ...formData, plan: e.target.value as Subscription['plan'] })}
                  aria-label="Change Plan"
                  title="Change Plan"
                >
                  <option value="Free">Free</option>
                  <option value="Basic">Basic</option>
                  <option value="Standard">Standard</option>
                  <option value="Enterprise">Enterprise</option>
                </select>
              </div>
              <div className="form-group">
                <label>Amount ($):</label>
                <input
                  type="number"
                  value={formData.amount ?? ''}
                  onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })}
                  className={formErrors.amount ? 'input-error' : ''}
                  placeholder="Enter amount"
                  title="Amount"
                />
                {formErrors.amount && <p className="error-message">{formErrors.amount}</p>}
              </div>
              <div className="form-group">
                <label>Next Bill Date:</label>
                <input
                  type="date"
                  value={formData.nextBill || ''}
                  onChange={(e) => setFormData({ ...formData, nextBill: e.target.value })}
                  placeholder="Select next bill date"
                  title="Next Bill Date"
                />
              </div>
              <div className="form-group">
                <label>Status:</label>
                <select
                  value={formData.status || ''}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value as Subscription['status'] })}
                  aria-label="Change Status"
                  title="Change Status"
                >
                  <option value="Active">Active</option>
                  <option value="Overdue">Overdue</option>
                  <option value="Cancelled">Cancelled</option>
                  <option value="Trial">Trial</option>
                </select>
              </div>
              <div className="modal-actions">
                <button type="button" className="button-secondary" onClick={() => setIsManageSubModalOpen(false)}>
                  Cancel
                </button>
                <button type="submit" className="button-primary">
                  <FiCheck size={18} /> Save Changes
                </button>
              </div>
            </form>
          </Modal>
        )}

        {/* View Invoices Modal */}
        {isViewInvoicesModalOpen && currentSubscription && (
          <Modal title={`Invoices for ${currentSubscription.tenant}`} onClose={() => setIsViewInvoicesModalOpen(false)}>
            <div className="invoice-list">
              {payments.filter(p => p.subscriptionId === currentSubscription.id).length === 0 ? (
                <p className="modal-message">No invoices found for this subscription.</p>
              ) : (
                payments.filter(p => p.subscriptionId === currentSubscription.id).map(p => (
                  <div key={p.id} className="invoice-item">
                    <span>Invoice #{p.id} - ${p.amount} ({p.date})</span>
                    <a href={p.invoiceUrl} target="_blank" rel="noopener noreferrer" className="text-link">View</a>
                  </div>
                ))
              )}
            </div>
            <div className="modal-actions">
              <button className="button-secondary" onClick={() => setIsViewInvoicesModalOpen(false)}>Close</button>
            </div>
          </Modal>
        )}

        {/* Send Reminder Confirmation Modal */}
        {isSendReminderModalOpen && currentSubscription && (
          <Modal title="Confirm Send Reminder" onClose={() => setIsSendReminderModalOpen(false)}>
            <p className="modal-message">
              Are you sure you want to send a payment reminder to **{currentSubscription.tenant}** for their **{currentSubscription.plan}** plan (ID: {currentSubscription.id})?
            </p>
            <div className="modal-actions">
              <button type="button" className="button-secondary" onClick={() => setIsSendReminderModalOpen(false)}>
                Cancel
              </button>
              <button type="button" className="button-warning" onClick={confirmSendReminder}>
                <FiZap size={18} /> Send Reminder
              </button>
            </div>
          </Modal>
        )}
      </div>

      {/* --- Inline Styles for Billing Component --- */}
      <style>{`
        /* Global Variables (consistent with dashboard and user management) */
        :root {
            --primary-color: #4f46e5;      /* Indigo-600 */
            --primary-dark: #4338ca;       /* Indigo-700 */
            --primary-light: #eef2ff;      /* Indigo-50 */
            --green-success: #10b981;      /* Emerald-500 */
            --green-light: #ecfdf5;        /* Emerald-50 */
            --red-danger: #ef4444;         /* Red-500 */
            --red-light: #fee2e2;          /* Red-50 */
            --yellow-warning: #f59e0b;     /* Amber-500 */
            --yellow-light: #fffbeb;       /* Amber-50 */
            --blue-info: #3b82f6;          /* Blue-500 */
            --blue-light: #eff6ff;         /* Blue-50 */
            --purple-accent: #8b5cf6;      /* Purple-500 */
            --purple-light: #f5f3ff;       /* Purple-50 */
            
            --text-dark: #1f2937;          /* Gray-900 */
            --text-medium: #4b5563;        /* Gray-700 */
            --text-light: #6b7280;         /* Gray-500 */
            --border-color: #e5e7eb;       /* Gray-200 */
            --bg-light: #f9fafb;           /* Gray-50 */
            --bg-gradient-start: #f0f4ff;
            --bg-gradient-end: #e6f0ff;
            
            --card-bg: white;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
        }

        .admin-page-container {
          min-height: 100vh;
          background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
          padding: 2.5rem 2rem;
          font-family: 'Inter', sans-serif;
          color: var(--text-dark);
        }

        .admin-page-wrapper {
          max-width: 1280px;
          margin: 0 auto;
        }

        /* Header */
        .admin-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
          background: var(--card-bg);
          padding: 1.5rem 2rem;
          border-radius: 0.75rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
        }

        .admin-title {
          font-size: 2rem;
          font-weight: 800;
          color: var(--primary-dark);
          letter-spacing: -0.03em;
          margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 0.75rem;
        }

        /* Overview Cards Grid */
        .billing-overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2.5rem;
        }

        .overview-card {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 1.8rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .overview-card .card-icon {
            padding: 0.75rem;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .card-icon.primary { background: var(--primary-light); color: var(--primary-color); }
        .card-icon.green { background: var(--green-light); color: var(--green-success); }
        .card-icon.red { background: var(--red-light); color: var(--red-danger); }
        .card-icon.yellow { background: var(--yellow-light); color: var(--yellow-warning); }


        .overview-card .card-label {
            font-size: 0.9rem;
            color: var(--text-light);
            margin: 0;
            font-weight: 500;
        }

        .overview-card .card-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-dark);
            margin: 0;
            letter-spacing: -0.03em;
        }

        /* Chart Card */
        .chart-card {
          background: var(--card-bg);
          border-radius: 0.75rem;
          padding: 1.8rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
          margin-bottom: 2.5rem;
        }

        .chart-header {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          margin-bottom: 1.5rem;
        }

        .chart-header h2 {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--text-dark);
          margin: 0 0 0.5rem 0;
        }

        .chart-subtitle {
          font-size: 0.9rem;
          color: var(--text-light);
          margin: 0;
        }

        .chart-container-small {
          height: 250px; /* Adjusted height for the Pie chart */
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        /* Filter Bar */
        .filter-bar {
          display: flex;
          gap: 1rem;
          margin-bottom: 1.5rem;
          background: var(--card-bg);
          padding: 1.2rem 1.5rem;
          border-radius: 0.75rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
          flex-wrap: wrap;
          align-items: center;
        }

        .search-input-wrapper {
          position: relative;
          flex-grow: 1;
          min-width: 200px;
        }

        .search-input {
          width: 100%;
          padding: 0.75rem 1rem 0.75rem 2.5rem;
          border: 1px solid var(--border-color);
          border-radius: 0.5rem;
          font-size: 0.95rem;
          color: var(--text-dark);
          transition: border-color 0.2s, box-shadow 0.2s;
        }

        .search-input:focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
          outline: none;
        }

        .search-icon {
          position: absolute;
          left: 0.8rem;
          top: 50%;
          transform: translateY(-50%);
          color: var(--text-light);
        }

        .filter-select {
          padding: 0.75rem 1rem;
          border: 1px solid var(--border-color);
          border-radius: 0.5rem;
          background-color: white;
          font-size: 0.95rem;
          color: var(--text-dark);
          cursor: pointer;
          appearance: none;
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd'%3E%3C/path%3E%3C/svg%3E");
          background-repeat: no-repeat;
          background-position: right 0.75rem center;
          background-size: 1.25rem;
          transition: border-color 0.2s;
        }

        .filter-select:focus {
          border-color: var(--primary-color);
          outline: none;
        }

        .clear-filters-button {
            background: none;
            border: none;
            color: var(--red-danger);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.4rem;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            transition: background 0.2s;
        }
        .clear-filters-button:hover {
            background: var(--red-light);
            color: var(--red-dark);
        }

        /* Table */
        .table-card {
          background: var(--card-bg);
          border-radius: 0.75rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
          overflow: hidden;
          margin-bottom: 2.5rem;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 1.8rem;
            border-bottom: 1px solid var(--border-color);
        }
        .table-header h2 {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-dark);
            margin: 0;
        }
        .view-all-button {
          background: transparent;
          border: none;
          color: var(--primary-color);
          font-size: 0.9rem;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 0.4rem;
          padding: 0.5rem 0.75rem;
          border-radius: 0.375rem;
          transition: background 0.2s, color 0.2s;
        }

        .view-all-button:hover {
          background: var(--primary-light);
          color: var(--primary-dark);
        }

        .table-wrapper {
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
        }

        table {
          width: 100%;
          border-collapse: collapse;
          min-width: 800px;
        }

        th {
          text-align: left;
          padding: 1rem 1.5rem;
          font-size: 0.85rem;
          font-weight: 600;
          color: var(--text-light);
          text-transform: uppercase;
          background: var(--bg-light);
          border-bottom: 1px solid var(--border-color);
        }

        td {
          padding: 1rem 1.5rem;
          font-size: 0.95rem;
          color: var(--text-medium);
          border-top: 1px solid var(--border-color);
        }

        tbody tr:last-child td {
          border-bottom: none;
        }

        tbody tr:hover {
          background-color: var(--bg-light);
        }

        .text-center-message {
            text-align: center;
            padding: 2rem 0;
            color: var(--text-light);
            font-style: italic;
        }

        .status-badge {
          display: inline-block;
          padding: 0.3rem 0.7rem;
          border-radius: 0.5rem;
          font-size: 0.75rem;
          font-weight: 700;
          text-transform: capitalize;
        }

        .status-active, .status-paid {
          background: var(--green-light);
          color: var(--green-success);
        }
        .status-overdue, .status-failed {
          background: var(--red-light);
          color: var(--red-danger);
        }
        .status-cancelled {
          background: var(--bg-light);
          color: var(--text-medium);
        }
        .status-trial {
            background: var(--blue-light);
            color: var(--blue-info);
        }
        .status-refunded {
            background: var(--yellow-light);
            color: var(--yellow-warning);
        }


        .action-buttons-cell {
          white-space: nowrap;
        }

        .action-button {
          background: var(--primary-light);
          color: var(--primary-color);
          padding: 0.6rem;
          border-radius: 0.375rem;
          border: none;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          transition: background 0.2s, transform 0.1s;
          margin-right: 0.5rem;
        }
        .action-button.primary:hover {
          background: var(--primary-color);
          color: white;
          transform: translateY(-1px);
        }
        .action-button.info {
            background: var(--blue-light);
            color: var(--blue-info);
        }
        .action-button.info:hover {
            background: var(--blue-info);
            color: white;
        }
        .action-button.warning {
            background: var(--yellow-light);
            color: var(--yellow-warning);
        }
        .action-button.warning:hover {
            background: var(--yellow-warning);
            color: white;
        }

        .text-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: text-decoration 0.2s;
        }
        .text-link:hover {
            text-decoration: underline;
        }


        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
          animation: fadeIn 0.2s ease-out;
        }

        .modal-content {
          background: white;
          padding: 2rem;
          border-radius: 0.75rem;
          box-shadow: var(--shadow-lg);
          max-width: 550px; /* Slightly wider modal */
          width: 90%;
          position: relative;
          animation: slideIn 0.3s ease-out;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideIn {
          from { transform: translateY(-30px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
          border-bottom: 1px solid var(--border-color);
          padding-bottom: 1rem;
        }

        .modal-header h2 {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--text-dark);
          margin: 0;
        }

        .modal-close-button {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: var(--text-light);
          transition: color 0.2s;
        }
        .modal-close-button:hover {
          color: var(--red-danger);
        }

        .form-group {
          margin-bottom: 1.2rem;
        }

        .form-group label {
          display: block;
          margin-bottom: 0.5rem;
          font-size: 0.9rem;
          font-weight: 600;
          color: var(--text-medium);
        }

        .form-group input,
        .form-group select {
          width: 100%;
          padding: 0.8rem 1rem;
          border: 1px solid var(--border-color);
          border-radius: 0.5rem;
          font-size: 0.95rem;
          color: var(--text-dark);
          transition: border-color 0.2s, box-shadow 0.2s;
          background-color: white;
        }

        .form-group input:focus,
        .form-group select:focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
          outline: none;
        }

        .form-group input.input-error,
        .form-group select.input-error {
            border-color: var(--red-danger);
        }

        .input-disabled {
            background-color: var(--bg-light);
            color: var(--text-light);
            cursor: not-allowed;
        }

        .error-message {
            color: var(--red-danger);
            font-size: 0.8rem;
            margin-top: 0.4rem;
        }

        .modal-actions {
          display: flex;
          justify-content: flex-end;
          gap: 0.75rem;
          margin-top: 2rem;
          padding-top: 1rem;
          border-top: 1px solid var(--border-color);
        }

        .button-primary, .button-secondary, .button-warning { /* Added .button-warning */
          padding: 0.8rem 1.5rem;
          border-radius: 0.5rem;
          border: none;
          font-weight: 600;
          font-size: 0.95rem;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          transition: all 0.2s ease-in-out;
        }

        .button-primary {
          background: var(--primary-color);
          color: white;
          box-shadow: var(--shadow-sm);
        }
        .button-primary:hover {
          background: var(--primary-dark);
          transform: translateY(-1px);
        }

        .button-secondary {
          background: var(--bg-light);
          color: var(--text-medium);
          border: 1px solid var(--border-color);
        }
        .button-secondary:hover {
          background: var(--border-color);
          color: var(--text-dark);
        }

        .button-warning {
            background: var(--yellow-warning);
            color: white;
            box-shadow: var(--shadow-sm);
        }
        .button-warning:hover {
            background: #d97706; /* Darker amber */
            transform: translateY(-1px);
        }

        .modal-message {
            font-size: 1rem;
            color: var(--text-medium);
            line-height: 1.5;
            margin-bottom: 1.5rem;
        }

        .invoice-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
        }
        .invoice-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px dashed var(--bg-light);
            font-size: 0.95rem;
            color: var(--text-medium);
        }
        .invoice-item:last-child {
            border-bottom: none;
        }

        /* Responsive adjustments */
        @media (max-width: 1024px) {
            .admin-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
                padding: 1.2rem 1.5rem;
            }
            .admin-title {
                font-size: 1.75rem;
            }
            .header-actions {
                width: 100%;
                justify-content: flex-start;
            }
            .filter-bar {
                flex-direction: column;
                align-items: stretch;
                gap: 0.8rem;
            }
            .search-input-wrapper {
                min-width: unset;
                width: 100%;
            }
            .filter-select {
                width: 100%;
            }
            .billing-overview-grid {
                grid-template-columns: 1fr; /* Stack cards on medium screens */
            }
        }

        @media (max-width: 768px) {
            .admin-page-container {
                padding: 1.5rem 1rem;
            }
            .admin-wrapper {
                padding: 0;
            }
            .admin-header, .filter-bar, .table-card, .overview-card, .chart-card {
                padding: 1rem;
            }
            .admin-title {
                font-size: 1.5rem;
            }
            th, td {
                padding: 0.8rem 1rem;
                font-size: 0.9rem;
            }
            .action-button {
                padding: 0.5rem;
                margin-right: 0.4rem;
            }
            .modal-content {
                padding: 1.5rem;
            }
            .modal-header h2 {
                font-size: 1.3rem;
            }
            .modal-actions {
                flex-direction: column;
                gap: 0.5rem;
            }
            .button-primary, .button-secondary, .button-warning {
                width: 100%;
                justify-content: center;
            }
            .chart-container-small {
                height: 200px; /* Smaller height for pie chart on small screens */
            }
        }
      `}</style>
    </div>
  );
};

// --- Modal Component (Reused from PlatformAdminUsers) ---
interface ModalProps {
  title: string;
  onClose: () => void;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ title, onClose, children }) => {
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>{title}</h2>
          <button className="modal-close-button" onClick={onClose} title="Close modal" aria-label="Close modal">
            <FiX size={24} />
          </button>
        </div>
        {children}
      </div>
    </div>
  );
};

export default Billing;