import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Eye, EyeOff } from 'lucide-react';
import { Link } from 'react-router-dom';

const Signup = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    institution: '',
    year: '',
    department: '',
    password: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Basic validation
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setIsLoading(false);
      return;
    }

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // In a real app, you would call your registration API here
      setSuccess(true);
      
      // Redirect after successful signup
      setTimeout(() => {
        navigate('/login');
      }, 2000);
    } catch (err) {
      setError('An error occurred during registration');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="signup-container">
      <div className="signup-card">
        <div className="signup-header">
          <h1>Exam Portal</h1>
          <p>Create your account</p>
        </div>

        <div className="signup-content">
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}
          
          {success ? (
            <div className="success-message">
              <h3>Registration Successful!</h3>
              <p>You will be redirected to the login page shortly.</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="signup-form">
              <div className="name-fields">
                <div className="form-group">
                  <label htmlFor="firstName">
                    First Name
                  </label>
                  <input
                    id="firstName"
                    name="firstName"
                    type="text"
                    required
                    value={formData.firstName}
                    onChange={handleChange}
                    placeholder="John"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="lastName">
                    Last Name
                  </label>
                  <input
                    id="lastName"
                    name="lastName"
                    type="text"
                    required
                    value={formData.lastName}
                    onChange={handleChange}
                    placeholder="Doe"
                  />
                </div>
              </div>
              
              <div className="form-group">
                <label htmlFor="email">
                  Email Address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="form-group">
                <label htmlFor="phone">
                  Phone Number
                </label>
                <input
                  id="phone"
                  name="phone"
                  type="tel"
                  required
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="+1234567890"
                />
              </div>

              <div className="form-group">
                <label htmlFor="institution">
                  Institution
                </label>
                <input
                  id="institution"
                  name="institution"
                  type="text"
                  required
                  value={formData.institution}
                  onChange={handleChange}
                  placeholder="Your school or university"
                />
              </div>

              <div className="additional-fields">
                <div className="form-group">
                  <label htmlFor="year">
                    Year
                  </label>
                  <input
                    id="year"
                    name="year"
                    type="text"
                    required
                    value={formData.year}
                    onChange={handleChange}
                    placeholder="e.g. 2023, Freshman, etc."
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="department">
                    Department
                  </label>
                  <input
                    id="department"
                    name="department"
                    type="text"
                    required
                    value={formData.department}
                    onChange={handleChange}
                    placeholder="Your field of study"
                  />
                </div>
              </div>
              
              <div className="form-group">
                <label htmlFor="password">
                  Password
                </label>
                <div className="password-input">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    required
                    minLength={8}
                    value={formData.password}
                    onChange={handleChange}
                    placeholder="••••••••"
                  />
                  <button
                    type="button"
                    className="toggle-password"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
                <p className="password-hint">
                  Must be at least 8 characters
                </p>
              </div>
              
              <div className="form-group">
                <label htmlFor="confirmPassword">
                  Confirm Password
                </label>
                <div className="password-input">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    required
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    placeholder="••••••••"
                  />
                  <button
                    type="button"
                    className="toggle-password"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
              </div>
              
              <div className="form-group terms-group">
                <input
                  id="terms"
                  name="terms"
                  type="checkbox"
                  required
                />
                <label htmlFor="terms">
                  I agree to the <Link to="/terms-of-service">Terms of Service</Link> and <Link to="/privacy-policy">Privacy Policy</Link>
                </label>
              </div>
              
              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className={`signup-button ${isLoading ? 'loading' : ''}`}
                >
                  {isLoading ? (
                    <>
                      <span className="spinner"></span>
                      Creating account...
                    </>
                  ) : 'Sign up'}
                </button>
              </div>
            </form>
          )}
          
          <div className="login-section">
            <div className="divider">
              <span>Already have an account?</span>
            </div>
            
            <div className="login-button">
              <a href="/login">
                Sign in
              </a>
            </div>
          </div>
        </div>
      </div>

      <style>{`
        .signup-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2rem;
        }

        .signup-card {
          width: 100%;
          max-width: 32rem;
          background: white;
          border-radius: 1rem;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .signup-header {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          padding: 2rem;
          text-align: center;
          color: white;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .signup-header h1 {
          font-size: 1.75rem;
          font-weight: 700;
          margin: 0;
          letter-spacing: -0.5px;
        }

        .signup-header p {
          color: rgba(255, 255, 255, 0.8);
          margin-top: 0.5rem;
          font-size: 0.9375rem;
        }

        .signup-content {
          padding: 2.5rem;
        }

        .error-message {
          margin-bottom: 1.5rem;
          padding: 1rem;
          background: #fef2f2;
          color: #dc2626;
          border-radius: 0.5rem;
          font-size: 0.875rem;
          border-left: 4px solid #dc2626;
        }

        .success-message {
          margin-bottom: 1.5rem;
          padding: 1rem;
          background: #f0fdf4;
          color: #16a34a;
          border-radius: 0.5rem;
          font-size: 0.875rem;
          text-align: center;
          border-left: 4px solid #16a34a;
        }

        .success-message h3 {
          margin: 0 0 0.5rem 0;
          font-size: 1rem;
          font-weight: 600;
        }

        .success-message p {
          margin: 0;
          font-size: 0.875rem;
        }

        .signup-form {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }

        .name-fields {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1.25rem;
        }

        .additional-fields {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1.25rem;
        }

        .form-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .form-group label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #374151;
        }

        .form-group input {
          width: 90%;
          padding: 0.75rem 1rem;
          border: 1px solid #e5e7eb;
          border-radius: 0.5rem;
          font-size: 0.9375rem;
          transition: all 0.2s;
          background-color: #f9fafb;
        }

        .form-group input:focus {
          outline: none;
          border-color: #818cf8;
          box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.2);
          background-color: white;
        }

        .form-group input::placeholder {
          color: #9ca3af;
          opacity: 1;
        }

        .password-input {
          position: relative;
        }

        .password-input input {
          padding-right: 2.75rem;
        }

        .toggle-password {
          position: absolute;
          right: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: #6b7280;
          cursor: pointer;
          padding: 0.25rem;
          transition: color 0.2s;
        }

        .toggle-password:hover {
          color: #4f46e5;
        }

        .password-hint {
          margin: 0.25rem 0 0 0;
          font-size: 0.75rem;
          color: #6b7280;
        }

        .terms-group {
          flex-direction: row;
          align-items: center;
          gap: 0.75rem;
          margin: 0.75rem 0;
        }

        .terms-group input {
          width: 1.125rem;
          height: 1.125rem;
          accent-color: #4f46e5;
          flex-shrink: 0;
        }

        .terms-group label {
          font-size: 0.8125rem;
          font-weight: 400;
          line-height: 1.4;
        }

        .terms-group a {
          color: #4f46e5;
          text-decoration: none;
          font-weight: 500;
          transition: text-decoration 0.2s;
        }

        .terms-group a:hover {
          text-decoration: underline;
        }

        .signup-button {
          width: 100%;
          padding: 0.875rem 1rem;
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          color: white;
          border: none;
          border-radius: 0.5rem;
          font-weight: 600;
          font-size: 0.9375rem;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.75rem;
          transition: all 0.2s;
          margin-top: 0.5rem;
        }

        .signup-button:hover {
          background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
        }

        .signup-button:active {
          transform: translateY(0);
        }

        .signup-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
          transform: none !important;
          box-shadow: none !important;
        }

        .signup-button.loading {
          opacity: 0.8;
        }

        .spinner {
          width: 1.125rem;
          height: 1.125rem;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          border-top-color: white;
          animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
          to { transform: rotate(360deg); }
        }

        .login-section {
          margin-top: 2rem;
        }

        .divider {
          position: relative;
          margin: 1.5rem 0;
          text-align: center;
        }

        .divider::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          height: 1px;
          background: #e5e7eb;
          z-index: 1;
        }

        .divider span {
          position: relative;
          padding: 0 1rem;
          background: white;
          color: #6b7280;
          font-size: 0.875rem;
          z-index: 2;
        }

        .login-button a {
          display: block;
          width: 100%;
          padding: 0.75rem 1rem;
          border: 1px solid #d1d5db;
          border-radius: 0.5rem;
          font-weight: 500;
          font-size: 0.9375rem;
          color: #374151;
          text-align: center;
          text-decoration: none;
          transition: all 0.2s;
          background-color: white;
        }

        .login-button a:hover {
          background: #f9fafb;
          border-color: #9ca3af;
        }
      `}</style>
    </div>
  );
};

export default Signup;