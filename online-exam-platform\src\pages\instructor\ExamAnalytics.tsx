import { useParams } from 'react-router-dom';
import { Download, Flag, BarChart2, <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle } from 'lucide-react';

const ExamAnalytics = () => {
  const { examId } = useParams();

  // Sample data
  const stats = {
    attempts: 120,
    averageScore: 78,
    passingRate: 85,
    flagged: 5,
    timeSpent: {
      average: "42 mins",
      distribution: [10, 30, 50, 10] // % of students in each time quartile
    },
    questionStats: [
      { id: 1, question: "What is React?", correctRate: 92, flagged: 2 },
      { id: 2, question: "Explain useEffect", correctRate: 65, flagged: 8 },
      { id: 3, question: "Closure in JS", correctRate: 78, flagged: 3 },
      { id: 4, question: "let vs const", correctRate: 88, flagged: 1 }
    ],
    flaggedSubmissions: [
      { id: 101, student: "<PERSON> Doe", score: 95, flags: 3, status: "reviewed" },
      { id: 102, student: "<PERSON>", score: 100, flags: 5, status: "pending" },
      { id: 103, student: "<PERSON>", score: 45, flags: 2, status: "pending" }
    ]
  };

  const exportData = (format: 'csv' | 'json') => {
    console.log(`Exporting data as ${format}`);
    alert(`Data exported as ${format.toUpperCase()}`);
  };

  return (
    <div className="exam-analytics-page">
      <div className="analytics-header">
        <div>
          <h1 className="analytics-title">
            <BarChart2 size={28} className="header-icon" />
            Exam Analytics: #{examId}
          </h1>
          <p className="analytics-subtitle">Detailed performance metrics and insights</p>
        </div>
        <div className="export-buttons">
          <button 
            onClick={() => exportData('csv')}
            className="export-button"
          >
            <Download size={18} className="button-icon" />
            Export CSV
          </button>
          <button 
            onClick={() => exportData('json')}
            className="export-button"
          >
            <Download size={18} className="button-icon" />
            Export JSON
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="stats-grid">
        <div className="stat-card attempts">
          <h2 className="stat-title">Total Attempts</h2>
          <p className="stat-value">{stats.attempts}</p>
          <p className="stat-trend positive">+12% from last exam</p>
        </div>
        <div className="stat-card average">
          <h2 className="stat-title">Average Score</h2>
          <p className="stat-value">{stats.averageScore}%</p>
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${stats.averageScore}%` }}
            ></div>
          </div>
        </div>
        <div className="stat-card passing">
          <h2 className="stat-title">Passing Rate</h2>
          <p className="stat-value">{stats.passingRate}%</p>
          <p className="stat-trend negative">5% below course average</p>
        </div>
        <div className="stat-card flagged">
          <h2 className="stat-title">
            <Flag size={18} className="stat-icon" />
            Flagged Submissions
          </h2>
          <p className="stat-value">{stats.flagged}</p>
          <p className="stat-trend alert">Require review</p>
        </div>
      </div>

      {/* Time Spent Analysis */}
      <div className="analytics-section">
        <h2 className="section-title">
          <svg className="section-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="1 1 100 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Time Spent Analysis
        </h2>
        <div className="time-analysis-content">
          <div className="time-distribution">
            <p className="time-average">Average time spent: <span>{stats.timeSpent.average}</span></p>
            <div className="distribution-chart">
              {stats.timeSpent.distribution.map((percent, index) => (
                <div key={index} className="chart-bar-container">
                  <div 
                    className="chart-bar"
                    style={{ height: `${percent}%` }}
                  ></div>
                  <span className="chart-label">Q{index + 1}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="time-details">
            <h3 className="details-title">Time Distribution</h3>
            <ul className="time-list">
              <li className="time-item">
                <span className="time-label">Fastest 25%:</span>
                <span className="time-value">Under 25 mins</span>
              </li>
              <li className="time-item">
                <span className="time-label">Middle 50%:</span>
                <span className="time-value">25-50 mins</span>
              </li>
              <li className="time-item">
                <span className="time-label">Slowest 25%:</span>
                <span className="time-value">Over 50 mins</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Question Performance */}
      <div className="analytics-section">
        <h2 className="section-title">Question Performance</h2>
        <div className="table-container">
          <table className="performance-table">
            <thead>
              <tr>
                <th className="table-header">Question</th>
                <th className="table-header">Correct Rate</th>
                <th className="table-header">Flagged</th>
                <th className="table-header">Actions</th>
              </tr>
            </thead>
            <tbody>
              {stats.questionStats.map((q) => (
                <tr key={q.id} className="table-row">
                  <td className="table-cell question-cell">{q.question}</td>
                  <td className="table-cell">
                    <div className="rate-container">
                      <span className="rate-value">{q.correctRate}%</span>
                      <div className="rate-bar">
                        <div 
                          className={`rate-fill ${
                            q.correctRate > 80 ? 'high' : 
                            q.correctRate > 60 ? 'medium' : 'low'
                          }`}
                          style={{ width: `${q.correctRate}%` }}
                        ></div>
                      </div>
                    </div>
                  </td>
                  <td className="table-cell">
                    <span className="flag-badge">
                      <Flag className="flag-icon" size={12} />
                      {q.flagged}
                    </span>
                  </td>
                  <td className="table-cell actions-cell">
                    <button className="action-button review">Review</button>
                    <button className="action-button details">Details</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Flagged Submissions */}
      <div className="analytics-section">
        <h2 className="section-title flagged-title">
          <AlertTriangle className="flagged-icon" size={20} />
          Flagged Submissions
        </h2>
        <div className="flagged-list">
          {stats.flaggedSubmissions.map((submission) => (
            <div key={submission.id} className="flagged-item">
              <div className="flagged-header">
                <div>
                  <h3 className="student-name">{submission.student}</h3>
                  <p className="student-score">Score: {submission.score}%</p>
                </div>
                <div className="flagged-status">
                  <span className={`status-badge ${
                    submission.status === 'reviewed' ? 'reviewed' : 'pending'
                  }`}>
                    {submission.status === 'reviewed' ? (
                      <CheckCircle className="status-icon" size={12} />
                    ) : (
                      <AlertTriangle className="status-icon" size={12} />
                    )}
                    {submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
                  </span>
                  <span className="flag-count">
                    <Flag className="flag-icon" size={12} />
                    {submission.flags} flags
                  </span>
                </div>
              </div>
              <div className="flagged-actions">
                <button className="action-button review">Review Submission</button>
                <button className="action-button details">View Details</button>
                {submission.status === 'pending' && (
                  <button className="action-button mark-reviewed">Mark as Reviewed</button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      <style>{`
        .exam-analytics-page {
          padding: 2rem;
          max-width: 1200px;
          margin: 0 auto;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .analytics-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
        }

        .analytics-title {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          font-size: 1.875rem;
          font-weight: 700;
          color: #15803d;
          margin-bottom: 0.5rem;
        }

        .analytics-subtitle {
          color: #64748b;
          font-size: 0.875rem;
        }

        .header-icon {
          color: #15803d;
        }

        .export-buttons {
          display: flex;
          gap: 0.75rem;
        }

        .export-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          background-color: white;
          color: #334155;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .export-button:hover {
          background-color: #f8fafc;
          border-color: #9ca3af;
        }

        .button-icon {
          margin-right: 0.25rem;
        }

        /* Stats Grid */
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 1.5rem;
          margin-bottom: 2rem;
        }

        .stat-card {
          background-color: white;
          padding: 1.5rem;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          position: relative;
          overflow: hidden;
        }

        .stat-card::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
        }

        .attempts::before { background-color: #15803d; }
        .average::before { background-color: #3b82f6; }
        .passing::before { background-color: #f59e0b; }
        .flagged::before { background-color: #ef4444; }

        .stat-title {
          font-size: 1rem;
          color: #64748b;
          margin-bottom: 0.5rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .stat-icon {
          color: inherit;
        }

        .stat-value {
          font-size: 2.25rem;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 0.5rem;
        }

        .stat-trend {
          font-size: 0.875rem;
          font-weight: 500;
        }

        .positive { color: #15803d; }
        .negative { color: #b45309; }
        .alert { color: #b91c1c; }

        .progress-bar {
          width: 100%;
          height: 0.5rem;
          background-color: #e5e7eb;
          border-radius: 0.25rem;
          overflow: hidden;
          margin-top: 0.75rem;
        }

        .progress-fill {
          height: 100%;
          background-color: #3b82f6;
          border-radius: 0.25rem;
        }

        /* Analytics Sections */
        .analytics-section {
          background-color: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          padding: 1.5rem;
          margin-bottom: 1.5rem;
        }

        .section-title {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1.5rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .flagged-title {
          color: #b91c1c;
        }

        .section-icon, .flagged-icon {
          color: #64748b;
        }

        .flagged-icon {
          color: #b91c1c;
        }

        /* Time Analysis */
        .time-analysis-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 2rem;
        }

        .time-average {
          color: #64748b;
          margin-bottom: 1.5rem;
        }

        .time-average span {
          font-weight: 600;
          color: #1f2937;
        }

        .distribution-chart {
          display: flex;
          height: 10rem;
          align-items: flex-end;
          gap: 1rem;
          margin-top: 1rem;
        }

        .chart-bar-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;
        }

        .chart-bar {
          width: 100%;
          background: linear-gradient(to top, #3b82f6, #60a5fa);
          border-radius: 0.25rem 0.25rem 0 0;
          transition: height 0.3s ease;
        }

        .chart-label {
          margin-top: 0.5rem;
          font-size: 0.75rem;
          color: #64748b;
        }

        .time-details {
          background-color: #f8fafc;
          padding: 1.25rem;
          border-radius: 0.5rem;
        }

        .details-title {
          font-weight: 600;
          color: #374151;
          margin-bottom: 1rem;
        }

        .time-list {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .time-item {
          display: flex;
          justify-content: space-between;
          padding: 0.5rem 0;
          border-bottom: 1px solid #e5e7eb;
        }

        .time-item:last-child {
          border-bottom: none;
        }

        .time-label {
          color: #64748b;
        }

        .time-value {
          font-weight: 500;
          color: #1f2937;
        }

        /* Question Performance Table */
        .table-container {
          overflow-x: auto;
        }

        .performance-table {
          width: 100%;
          border-collapse: collapse;
        }

        .table-header {
          text-align: left;
          padding: 0.75rem 1rem;
          background-color: #f8fafc;
          color: #64748b;
          font-weight: 500;
          text-transform: uppercase;
          font-size: 0.75rem;
          letter-spacing: 0.05em;
        }

        .table-row {
          border-bottom: 1px solid #e5e7eb;
        }

        .table-row:last-child {
          border-bottom: none;
        }

        .table-cell {
          padding: 1rem;
          vertical-align: middle;
        }

        .question-cell {
          font-weight: 500;
          color: #1f2937;
        }

        .rate-container {
          display: flex;
          align-items: center;
          gap: 0.75rem;
        }

        .rate-value {
          min-width: 2.5rem;
          font-weight: 500;
        }

        .rate-bar {
          flex-grow: 1;
          height: 0.5rem;
          background-color: #e5e7eb;
          border-radius: 0.25rem;
          overflow: hidden;
        }

        .rate-fill {
          height: 100%;
          border-radius: 0.25rem;
        }

        .high { background-color: #10b981; }
        .medium { background-color: #f59e0b; }
        .low { background-color: #ef4444; }

        .flag-badge {
          display: inline-flex;
          align-items: center;
          padding: 0.25rem 0.5rem;
          background-color: #fee2e2;
          color: #b91c1c;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 500;
        }

        .flag-icon {
          margin-right: 0.25rem;
        }

        .actions-cell {
          white-space: nowrap;
        }

        .action-button {
          padding: 0.375rem 0.75rem;
          border-radius: 0.25rem;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
          border: none;
        }

        .review {
          background-color: #dbeafe;
          color: #1d4ed8;
          margin-right: 0.5rem;
        }

        .review:hover {
          background-color: #bfdbfe;
        }

        .details {
          background-color: #e5e7eb;
          color: #374151;
        }

        .details:hover {
          background-color: #d1d5db;
        }

        /* Flagged Submissions */
        .flagged-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .flagged-item {
          border: 1px solid #e5e7eb;
          border-radius: 0.5rem;
          padding: 1rem;
          transition: background-color 0.2s;
        }

        .flagged-item:hover {
          background-color: #f8fafc;
        }

        .flagged-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 0.75rem;
        }

        .student-name {
          font-weight: 500;
          color: #1f2937;
          margin-bottom: 0.25rem;
        }

        .student-score {
          color: #64748b;
          font-size: 0.875rem;
        }

        .flagged-status {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .status-badge {
          display: inline-flex;
          align-items: center;
          padding: 0.25rem 0.5rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 500;
        }

        .reviewed {
          background-color: #dcfce7;
          color: #166534;
        }

        .pending {
          background-color: #fef3c7;
          color: #92400e;
        }

        .status-icon {
          margin-right: 0.25rem;
        }

        .flag-count {
          display: inline-flex;
          align-items: center;
          padding: 0.25rem 0.5rem;
          background-color: #fee2e2;
          color: #b91c1c;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 500;
        }

        .flagged-actions {
          display: flex;
          gap: 0.75rem;
        }

        .mark-reviewed {
          background-color: #dcfce7;
          color: #166534;
          margin-left: auto;
        }

        .mark-reviewed:hover {
          background-color: #bbf7d0;
        }

        /* Responsive */
        @media (max-width: 1024px) {
          .stats-grid {
            grid-template-columns: repeat(2, 1fr);
          }
          
          .time-analysis-content {
            grid-template-columns: 1fr;
          }
        }

        @media (max-width: 640px) {
          .analytics-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
          }
          
          .stats-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default ExamAnalytics;