import React, { useState, useEffect } from 'react';
import { FiEdit, FiTrash2, FiPlus, FiSearch, FiX, FiCheck, FiUserPlus } from 'react-icons/fi';

// Define types for better type safety
interface User {
  id: number;
  name: string;
  email: string;
  role: 'Platform Admin' | 'University Admin' | 'Viewer' | ''; // Define possible roles
  status: 'Active' | 'Inactive';
  university?: string; // Optional for Platform Admins
}

const PlatformAdminUsers = () => {
  // Mock data for initial user list
  const initialPlatformUserList: User[] = [
    { id: 1, name: 'Tresor <PERSON>yum', email: '<EMAIL>', role: 'Platform Admin', status: 'Active' },
    { id: 2, name: '<PERSON><PERSON><PERSON> Essaid', email: '<EMAIL>', role: 'University Admin', status: 'Active', university: 'University A' },
    { id: 3, name: '<PERSON><PERSON><PERSON><PERSON>', email: '<EMAIL>', role: 'University Admin', status: 'Inactive', university: 'University B' },
    { id: 4, name: '<PERSON><PERSON>', email: '<EMAIL>', role: 'Viewer', status: 'Active', university: 'University C' },
    { id: 5, name: 'Alice Wonderland', email: '<EMAIL>', role: 'University Admin', status: 'Active', university: 'University D' },
  ];

  // State for user data and UI interactions
  const [users, setUsers] = useState<User[]>(initialPlatformUserList);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null); // For editing or deactivating
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [formErrors, setFormErrors] = useState<Partial<Record<keyof User, string>>>({});

  // Form state for Add/Edit User
  const [formData, setFormData] = useState<Omit<User, 'id' | 'status'>>({
    name: '',
    email: '',
    role: '',
    university: '',
  });

  // --- Helper Functions ---
  const validateForm = () => {
    const errors: Partial<Record<keyof User, string>> = {};
    if (!formData.name.trim()) errors.name = 'Name is required';
    if (!formData.email.trim()) errors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(formData.email)) errors.email = 'Invalid email format';
    if (!formData.role) errors.role = 'Role is required';
    if (formData.role === 'University Admin' && !formData.university?.trim()) {
      errors.university = 'University is required for University Admins';
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const resetForm = () => {
    setFormData({ name: '', email: '', role: '', university: '' });
    setFormErrors({});
  };

  const getStatusBadgeClass = (status: 'Active' | 'Inactive') => {
    return status === 'Active' ? 'status-active' : 'status-inactive';
  };

  // --- CRUD Operations ---

  // Create User
  const handleAddUser = () => {
    if (!validateForm()) return;
    const newUser: User = {
      id: users.length > 0 ? Math.max(...users.map(u => u.id)) + 1 : 1,
      name: formData.name,
      email: formData.email,
      role: formData.role as User['role'], // Type assertion based on validation
      status: 'Active', // New users are active by default
      university: formData.role === 'University Admin' ? formData.university : undefined,
    };
    setUsers([...users, newUser]);
    setIsAddModalOpen(false);
    resetForm();
  };

  // Edit User (Prepare form)
  const handleEditClick = (user: User) => {
    setCurrentUser(user);
    setFormData({
      name: user.name,
      email: user.email,
      role: user.role,
      university: user.university || '',
    });
    setIsEditModalOpen(true);
  };

  // Update User
  const handleUpdateUser = () => {
    if (!currentUser || !validateForm()) return;
    setUsers(users.map(user =>
      user.id === currentUser.id
        ? {
            ...user,
            name: formData.name,
            email: formData.email,
            role: formData.role as User['role'],
            university: formData.role === 'University Admin' ? formData.university : undefined,
          }
        : user
    ));
    setIsEditModalOpen(false);
    setCurrentUser(null);
    resetForm();
  };

  // Deactivate User (Prepare confirmation)
  const handleDeactivateClick = (user: User) => {
    setCurrentUser(user);
    setIsConfirmModalOpen(true);
  };

  // Confirm Deactivate User
  const handleConfirmDeactivate = () => {
    if (currentUser) {
      setUsers(users.map(user =>
        user.id === currentUser.id
          ? { ...user, status: 'Inactive' }
          : user
      ));
    }
    setIsConfirmModalOpen(false);
    setCurrentUser(null);
  };

  // --- Filtering and Searching ---
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = filterRole ? user.role === filterRole : true;
    const matchesStatus = filterStatus ? user.status === filterStatus : true;
    return matchesSearch && matchesRole && matchesStatus;
  });

  // --- Rendered JSX ---
  return (
    <div className="admin-page-container">
      <div className="admin-page-wrapper">
        <div className="admin-header">
          <h1 className="admin-title">Platform User Management</h1>
          <button
            className="add-user-button"
            onClick={() => {
              resetForm(); // Ensure form is clear for new user
              setIsAddModalOpen(true);
            }}
          >
            <FiUserPlus size={18} /> Add New User
          </button>
        </div>

        {/* Search and Filter Bar */}
        <div className="filter-bar">
          <div className="search-input-wrapper">
            <FiSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          <select
            value={filterRole}
            onChange={(e) => setFilterRole(e.target.value)}
            className="filter-select"
            aria-label="Filter by role"
          >
            <option value="">All Roles</option>
            <option value="Platform Admin">Platform Admin</option>
            <option value="University Admin">University Admin</option>
            <option value="Viewer">Viewer</option>
          </select>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="filter-select"
            aria-label="Filter by status"
          >
            <option value="">All Statuses</option>
            <option value="Active">Active</option>
            <option value="Inactive">Inactive</option>
          </select>
          {(searchTerm || filterRole || filterStatus) && (
            <button className="clear-filters-button" onClick={() => {
              setSearchTerm('');
              setFilterRole('');
              setFilterStatus('');
            }}>
              Clear Filters <FiX size={16} />
            </button>
          )}
        </div>

        {/* User Table */}
        <div className="table-card">
          <div className="table-wrapper">
            <table>
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Role</th>
                  <th>Status</th>
                  <th>University</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="text-center-message">No users found matching your criteria.</td>
                  </tr>
                ) : (
                  filteredUsers.map((user) => (
                    <tr key={user.id}>
                      <td>{user.name}</td>
                      <td>{user.email}</td>
                      <td>{user.role || 'N/A'}</td>
                      <td>
                        <span className={`status-badge ${getStatusBadgeClass(user.status)}`}>
                          {user.status}
                        </span>
                      </td>
                      <td>{user.university || 'N/A'}</td>
                      <td className="action-buttons-cell">
                        <button
                          className="action-button edit"
                          onClick={() => handleEditClick(user)}
                          title="Edit User"
                        >
                          <FiEdit size={16} />
                        </button>
                        <button
                          className="action-button deactivate"
                          onClick={() => handleDeactivateClick(user)}
                          disabled={user.status === 'Inactive'}
                          title={user.status === 'Inactive' ? 'User already inactive' : 'Deactivate User'}
                        >
                          <FiTrash2 size={16} />
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Add User Modal */}
        {isAddModalOpen && (
          <Modal title="Add New Platform User" onClose={() => setIsAddModalOpen(false)}>
            <form onSubmit={(e) => { e.preventDefault(); handleAddUser(); }}>
              <div className="form-group">
                <label>Name:</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className={formErrors.name ? 'input-error' : ''}
                  placeholder="Enter name"
                  title="Name"
                />
                {formErrors.name && <p className="error-message">{formErrors.name}</p>}
              </div>
              <div className="form-group">
                <label htmlFor="add-user-email">Email:</label>
                <input
                  id="add-user-email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className={formErrors.email ? 'input-error' : ''}
                  placeholder="Enter email address"
                  title="Email"
                />
                {formErrors.email && <p className="error-message">{formErrors.email}</p>}
              </div>
              <div className="form-group">
                <label>Role:</label>
                <select
                  value={formData.role}
                  onChange={(e) => setFormData({ ...formData, role: e.target.value as User['role'], university: e.target.value !== 'University Admin' ? '' : formData.university })}
                  className={formErrors.role ? 'input-error' : ''}
                  title="Role"
                >
                  <option value="">Select Role</option>
                  <option value="Platform Admin">Platform Admin</option>
                  <option value="University Admin">University Admin</option>
                  <option value="Viewer">Viewer</option>
                </select>
                {formErrors.role && <p className="error-message">{formErrors.role}</p>}
              </div>
              {formData.role === 'University Admin' && (
                <div className="form-group">
                  <label>University:</label>
                  <input
                    type="text"
                    value={formData.university}
                    onChange={(e) => setFormData({ ...formData, university: e.target.value })}
                    className={formErrors.university ? 'input-error' : ''}
                    placeholder="Enter university name"
                  />
                  {formErrors.university && <p className="error-message">{formErrors.university}</p>}
                </div>
              )}
              <div className="modal-actions">
                <button type="button" className="button-secondary" onClick={() => setIsAddModalOpen(false)}>
                  Cancel
                </button>
                <button type="submit" className="button-primary">
                  <FiPlus size={18} /> Add User
                </button>
              </div>
            </form>
          </Modal>
        )}

        {/* Edit User Modal */}
        {isEditModalOpen && currentUser && (
          <Modal title={`Edit User: ${currentUser.name}`} onClose={() => setIsEditModalOpen(false)}>
            <form onSubmit={(e) => { e.preventDefault(); handleUpdateUser(); }}>
              <div className="form-group">
                <label>Name:</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className={formErrors.name ? 'input-error' : ''}
                  title="Name"
                  placeholder="Enter name"
                />
                {formErrors.name && <p className="error-message">{formErrors.name}</p>}
              </div>
              <div className="form-group">
                <label>Email:</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className={formErrors.email ? 'input-error' : ''}
                  title="Email"
                  placeholder="Enter email address"
                />
                {formErrors.email && <p className="error-message">{formErrors.email}</p>}
              </div>
              <div className="form-group">
                <label>Role:</label>
                <select
                  value={formData.role}
                  onChange={(e) => setFormData({ ...formData, role: e.target.value as User['role'], university: e.target.value !== 'University Admin' ? '' : formData.university })}
                  className={formErrors.role ? 'input-error' : ''}
                >
                  <option value="">Select Role</option>
                  <option value="Platform Admin">Platform Admin</option>
                  <option value="University Admin">University Admin</option>
                  <option value="Viewer">Viewer</option>
                </select>
                {formErrors.role && <p className="error-message">{formErrors.role}</p>}
              </div>
              {formData.role === 'University Admin' && (
                <div className="form-group">
                  <label>University:</label>
                  <input
                    type="text"
                    value={formData.university}
                    onChange={(e) => setFormData({ ...formData, university: e.target.value })}
                    className={formErrors.university ? 'input-error' : ''}
                    title="University"
                    placeholder="Enter university name"
                  />
                  {formErrors.university && <p className="error-message">{formErrors.university}</p>}
                </div>
              )}
              <div className="modal-actions">
                <button type="button" className="button-secondary" onClick={() => setIsEditModalOpen(false)}>
                  Cancel
                </button>
                <button type="submit" className="button-primary">
                  <FiCheck size={18} /> Update User
                </button>
              </div>
            </form>
          </Modal>
        )}

        {/* Deactivation Confirmation Modal */}
        {isConfirmModalOpen && currentUser && (
          <Modal title="Confirm Deactivation" onClose={() => setIsConfirmModalOpen(false)}>
            <p className="modal-message">
              Are you sure you want to deactivate **{currentUser.name}**? This action can be reverted manually if needed.
            </p>
            <div className="modal-actions">
              <button type="button" className="button-secondary" onClick={() => setIsConfirmModalOpen(false)}>
                Cancel
              </button>
              <button type="button" className="button-danger" onClick={handleConfirmDeactivate}>
                <FiTrash2 size={18} /> Deactivate
              </button>
            </div>
          </Modal>
        )}
      </div>

      {/* --- Inline Styles for PlatformAdminUsers --- */}
      <style>{`
        /* Global Variables (consistent with dashboard) */
        :root {
            --primary-color: #4f46e5;      /* Indigo-600 */
            --primary-dark: #4338ca;       /* Indigo-700 */
            --primary-light: #eef2ff;      /* Indigo-50 */
            --green-success: #10b981;      /* Emerald-500 */
            --green-light: #ecfdf5;        /* Emerald-50 */
            --red-danger: #ef4444;         /* Red-500 */
            --red-light: #fee2e2;          /* Red-50 */
            --yellow-warning: #f59e0b;     /* Amber-500 */
            --yellow-light: #fffbeb;       /* Amber-50 */
            --blue-info: #3b82f6;          /* Blue-500 */
            --blue-light: #eff6ff;         /* Blue-50 */
            --purple-accent: #8b5cf6;      /* Purple-500 */
            --purple-light: #f5f3ff;       /* Purple-50 */
            
            --text-dark: #1f2937;          /* Gray-900 */
            --text-medium: #4b5563;        /* Gray-700 */
            --text-light: #6b7280;         /* Gray-500 */
            --border-color: #e5e7eb;       /* Gray-200 */
            --bg-light: #f9fafb;           /* Gray-50 */
            --bg-gradient-start: #f0f4ff;
            --bg-gradient-end: #e6f0ff;
            
            --card-bg: white;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
        }

        .admin-page-container {
          min-height: 100vh;
          background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
          padding: 2.5rem 2rem;
          font-family: 'Inter', sans-serif;
          color: var(--text-dark);
        }

        .admin-page-wrapper {
          max-width: 1280px;
          margin: 0 auto;
        }

        /* Header */
        .admin-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
          background: var(--card-bg);
          padding: 1.5rem 2rem;
          border-radius: 0.75rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
        }

        .admin-title {
          font-size: 2rem;
          font-weight: 800;
          color: var(--primary-dark);
          letter-spacing: -0.03em;
          margin: 0;
        }

        .add-user-button {
          background: var(--primary-color);
          color: white;
          padding: 0.8rem 1.5rem;
          border-radius: 0.5rem;
          border: none;
          font-weight: 600;
          font-size: 1rem;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          transition: background 0.2s ease-in-out, transform 0.1s ease-in-out;
          box-shadow: var(--shadow-sm);
        }

        .add-user-button:hover {
          background: var(--primary-dark);
          transform: translateY(-1px);
        }

        /* Filter Bar */
        .filter-bar {
          display: flex;
          gap: 1rem;
          margin-bottom: 1.5rem;
          background: var(--card-bg);
          padding: 1.2rem 1.5rem;
          border-radius: 0.75rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
          flex-wrap: wrap; /* Allow wrapping on smaller screens */
          align-items: center;
        }

        .search-input-wrapper {
          position: relative;
          flex-grow: 1;
          min-width: 200px; /* Minimum width for search */
        }

        .search-input {
          width: 100%;
          padding: 0.75rem 1rem 0.75rem 2.5rem; /* Left padding for icon */
          border: 1px solid var(--border-color);
          border-radius: 0.5rem;
          font-size: 0.95rem;
          color: var(--text-dark);
          transition: border-color 0.2s, box-shadow 0.2s;
        }

        .search-input:focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
          outline: none;
        }

        .search-icon {
          position: absolute;
          left: 0.8rem;
          top: 50%;
          transform: translateY(-50%);
          color: var(--text-light);
        }

        .filter-select {
          padding: 0.75rem 1rem;
          border: 1px solid var(--border-color);
          border-radius: 0.5rem;
          background-color: white;
          font-size: 0.95rem;
          color: var(--text-dark);
          cursor: pointer;
          appearance: none; /* Remove default arrow */
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd'%3E%3C/path%3E%3C/svg%3E"); /* Custom arrow */
          background-repeat: no-repeat;
          background-position: right 0.75rem center;
          background-size: 1.25rem;
          transition: border-color 0.2s;
        }

        .filter-select:focus {
          border-color: var(--primary-color);
          outline: none;
        }

        .clear-filters-button {
            background: none;
            border: none;
            color: var(--red-danger);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.4rem;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            transition: background 0.2s;
        }
        .clear-filters-button:hover {
            background: var(--red-light);
            color: var(--red-dark);
        }


        /* Table */
        .table-card {
          background: var(--card-bg);
          border-radius: 0.75rem;
          box-shadow: var(--shadow-sm);
          border: 1px solid var(--border-color);
          overflow: hidden; /* Ensures rounded corners */
          margin-bottom: 2.5rem;
        }

        .table-wrapper {
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
        }

        table {
          width: 100%;
          border-collapse: collapse;
          min-width: 800px; /* Ensure table doesn't get too small horizontally */
        }

        th {
          text-align: left;
          padding: 1rem 1.5rem;
          font-size: 0.85rem;
          font-weight: 600;
          color: var(--text-light);
          text-transform: uppercase;
          background: var(--bg-light);
          border-bottom: 1px solid var(--border-color);
        }

        td {
          padding: 1rem 1.5rem;
          font-size: 0.95rem;
          color: var(--text-medium);
          border-top: 1px solid var(--border-color);
        }

        tbody tr:last-child td {
          border-bottom: none;
        }

        tbody tr:hover {
          background-color: var(--bg-light);
        }

        .text-center-message {
            text-align: center;
            padding: 2rem 0;
            color: var(--text-light);
            font-style: italic;
        }

        .status-badge {
          display: inline-block;
          padding: 0.3rem 0.7rem;
          border-radius: 0.5rem;
          font-size: 0.75rem;
          font-weight: 700;
          text-transform: capitalize;
        }

        .status-active {
          background: var(--green-light);
          color: var(--green-success);
        }

        .status-inactive {
          background: var(--red-light);
          color: var(--red-danger);
        }

        .action-buttons-cell {
          white-space: nowrap; /* Prevent buttons from wrapping */
        }

        .action-button {
          background: var(--primary-light);
          color: var(--primary-color);
          padding: 0.6rem;
          border-radius: 0.375rem;
          border: none;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          transition: background 0.2s, transform 0.1s;
          margin-right: 0.5rem;
        }

        .action-button:hover {
          background: var(--primary-color);
          color: white;
          transform: translateY(-1px);
        }

        .action-button.deactivate {
          background: var(--red-light);
          color: var(--red-danger);
        }
        .action-button.deactivate:hover {
          background: var(--red-danger);
          color: white;
        }
        .action-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background: var(--bg-light);
            color: var(--text-light);
        }
        .action-button.deactivate:disabled {
            background: var(--red-light);
            color: var(--red-danger);
            opacity: 0.4;
        }


        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
          animation: fadeIn 0.2s ease-out;
        }

        .modal-content {
          background: white;
          padding: 2rem;
          border-radius: 0.75rem;
          box-shadow: var(--shadow-lg);
          max-width: 500px;
          width: 90%;
          position: relative;
          animation: slideIn 0.3s ease-out;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideIn {
          from { transform: translateY(-30px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
          border-bottom: 1px solid var(--border-color);
          padding-bottom: 1rem;
        }

        .modal-header h2 {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--text-dark);
          margin: 0;
        }

        .modal-close-button {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: var(--text-light);
          transition: color 0.2s;
        }
        .modal-close-button:hover {
          color: var(--red-danger);
        }

        .form-group {
          margin-bottom: 1.2rem;
        }

        .form-group label {
          display: block;
          margin-bottom: 0.5rem;
          font-size: 0.9rem;
          font-weight: 600;
          color: var(--text-medium);
        }

        .form-group input,
        .form-group select {
          width: 100%;
          padding: 0.8rem 1rem;
          border: 1px solid var(--border-color);
          border-radius: 0.5rem;
          font-size: 0.95rem;
          color: var(--text-dark);
          transition: border-color 0.2s, box-shadow 0.2s;
          background-color: white; /* Ensure consistent background */
        }

        .form-group input:focus,
        .form-group select:focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
          outline: none;
        }

        .form-group input.input-error,
        .form-group select.input-error {
            border-color: var(--red-danger);
        }

        .error-message {
            color: var(--red-danger);
            font-size: 0.8rem;
            margin-top: 0.4rem;
        }

        .modal-actions {
          display: flex;
          justify-content: flex-end;
          gap: 0.75rem;
          margin-top: 2rem;
          padding-top: 1rem;
          border-top: 1px solid var(--border-color);
        }

        .button-primary, .button-secondary, .button-danger {
          padding: 0.8rem 1.5rem;
          border-radius: 0.5rem;
          border: none;
          font-weight: 600;
          font-size: 0.95rem;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          transition: all 0.2s ease-in-out;
        }

        .button-primary {
          background: var(--primary-color);
          color: white;
          box-shadow: var(--shadow-sm);
        }
        .button-primary:hover {
          background: var(--primary-dark);
          transform: translateY(-1px);
        }

        .button-secondary {
          background: var(--bg-light);
          color: var(--text-medium);
          border: 1px solid var(--border-color);
        }
        .button-secondary:hover {
          background: var(--border-color);
          color: var(--text-dark);
        }

        .button-danger {
          background: var(--red-danger);
          color: white;
          box-shadow: var(--shadow-sm);
        }
        .button-danger:hover {
          background: #dc2626; /* Darker red */
          transform: translateY(-1px);
        }

        .modal-message {
            font-size: 1rem;
            color: var(--text-medium);
            line-height: 1.5;
            margin-bottom: 1.5rem;
        }

        /* Responsive adjustments */
        @media (max-width: 1024px) {
            .admin-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
                padding: 1.2rem 1.5rem;
            }
            .admin-title {
                font-size: 1.75rem;
            }
            .filter-bar {
                flex-direction: column;
                align-items: stretch;
                gap: 0.8rem;
            }
            .search-input-wrapper {
                min-width: unset;
                width: 100%;
            }
            .filter-select {
                width: 100%;
            }
        }

        @media (max-width: 768px) {
            .admin-page-container {
                padding: 1.5rem 1rem;
            }
            .admin-header, .filter-bar, .table-card {
                padding: 1rem;
            }
            .admin-title {
                font-size: 1.5rem;
            }
            th, td {
                padding: 0.8rem 1rem;
                font-size: 0.9rem;
            }
            .action-button {
                padding: 0.5rem;
                margin-right: 0.4rem;
            }
            .modal-content {
                padding: 1.5rem;
            }
            .modal-header h2 {
                font-size: 1.3rem;
            }
            .modal-actions {
                flex-direction: column;
                gap: 0.5rem;
            }
            .button-primary, .button-secondary, .button-danger {
                width: 100%;
                justify-content: center;
            }
        }
      `}</style>
    </div>
  );
};

// --- Modal Component (moved outside for reusability) ---
interface ModalProps {
  title: string;
  onClose: () => void;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ title, onClose, children }) => {
  useEffect(() => {
    // Disable scrolling on mount
    document.body.style.overflow = 'hidden';
    return () => {
      // Re-enable scrolling on unmount
      document.body.style.overflow = 'unset';
    };
  }, []);

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>{title}</h2>
          <button className="modal-close-button" onClick={onClose} title="Close modal">
            <FiX size={24} />
          </button>
        </div>
        {children}
      </div>
    </div>
  );
};

export default PlatformAdminUsers;