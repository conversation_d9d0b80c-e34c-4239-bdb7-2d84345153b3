import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  User,
  UserCog,
  UserCheck,
  Upload,
  ArrowRight,
  KeyRound, // Added for access code
  CheckCircle, // For success messages
  XCircle // For error messages
} from 'lucide-react';

const ProfileSetup = () => {
  const [name, setName] = useState('');
  const [role, setRole] = useState('student');
  const [photo, setPhoto] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [accessCode, setAccessCode] = useState(''); // State for access code input
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const navigate = useNavigate();

  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setPhoto(file);
    
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setPreview(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage(null); // Clear previous messages

    // Simulate access code verification (replace with actual backend verification)
    const MOCK_VALID_ACCESS_CODE = "ETBPVQY4KY"; // This should come from your PaymentSuccessPage or backend
    if (accessCode !== MOCK_VALID_ACCESS_CODE) {
      setMessage({ type: 'error', text: 'Invalid access code. Please check your code.' });
      setIsSubmitting(false);
      return;
    }

    // Simulate profile setup submission
    try {
      // In a real app, you'd send data (name, role, photo, accessCode) to a server
      console.log(`Submitting: Name: ${name}, Role: ${role}, Photo: ${photo?.name || 'None'}, Access Code: ${accessCode}`);
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API call

      setMessage({ type: 'success', text: 'Profile setup complete! Redirecting...' });
      setTimeout(() => {
        navigate('/Device-Check'); // Navigate to Device Check page
      }, 1000);
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to set up profile. Please try again.' });
      console.error("Profile setup error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="profile-setup-page">
      <div className="setup-container">
        <form onSubmit={handleSubmit} className="setup-form">
          <div className="form-header">
            <UserCog size={48} className="header-icon" />
            <h2 className="form-title">Complete Your Profile</h2>
            <p className="form-subtitle">Tell us a bit about yourself to get started</p>
          </div>

          {message && (
            <div className={`message-box ${message.type}`}>
              {message.type === 'success' && <CheckCircle size={20} />}
              {message.type === 'error' && <XCircle size={20} />}
              <span>{message.text}</span>
            </div>
          )}

          <div className="form-group">
            <label htmlFor="accessCode" className="form-label">
              Access Code
            </label>
            <div className="input-with-icon">
              <KeyRound size={20} className="input-icon" />
              <input
                type="text"
                id="accessCode"
                placeholder="Enter your unique access code"
                value={accessCode}
                onChange={(e) => setAccessCode(e.target.value)}
                className="form-input"
                required
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="name" className="form-label">
              Full Name
            </label>
            <div className="input-with-icon">
              <User size={20} className="input-icon" />
              <input
                type="text"
                id="name"
                placeholder="Enter your full name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="form-input"
                required
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="role" className="form-label">
              Your Role
            </label>
            <div className="select-wrapper input-with-icon">
              <UserCheck size={20} className="input-icon" />
              <select
                id="role"
                value={role}
                onChange={(e) => setRole(e.target.value)}
                className="form-input"
              >
                <option value="student">Student</option>
                <option value="instructor">Instructor</option>
                <option value="proctor">Proctor</option>
              </select>
              <div className="select-arrow">
                <svg viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="photo-upload" className="form-label">
              Profile Photo (Optional)
            </label>
            <div className="photo-upload-area">
              {preview ? (
                <div className="photo-preview">
                  <img src={preview} alt="Profile Preview" className="preview-image" />
                  <button type="button" onClick={() => { setPhoto(null); setPreview(null); }} className="remove-photo-button">
                    Remove Photo
                  </button>
                </div>
              ) : (
                <label htmlFor="photo-upload" className="upload-button">
                  <Upload size={24} className="upload-icon" />
                  <span>Upload Photo</span>
                  <input
                    type="file"
                    id="photo-upload"
                    accept="image/*"
                    onChange={handlePhotoChange}
                    className="hidden-input"
                  />
                </label>
              )}
            </div>
          </div>

          <button type="submit" className="submit-button" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <ArrowRight size={18} className="button-icon animate-spin" />
                Setting up...
              </>
            ) : (
              <>
                Complete Setup
                <ArrowRight size={18} className="button-icon" />
              </>
            )}
          </button>
        </form>
      </div>

      <style>{`
        :root {
            --primary-color: #4f46e5;
            --primary-dark: #4338ca;
            --primary-light: #eef2ff;
            --green-success: #10b981;
            --green-light: #ecfdf5;
            --text-dark: #1f2937;
            --text-medium: #4b5563;
            --text-light: #6b7280;
            --border-color: #e5e7eb;
            --bg-light: #f9fafb;
            --bg-gradient-start: #f0f4ff;
            --bg-gradient-end: #e6f0ff;
            --red-error: #ef4444;
            --red-light: #fee2e2;
        }

        .profile-setup-page {
          min-height: 100vh;
          display: flex;
          justify-content: center;
          align-items: center;
          background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
          padding: 2rem;
          font-family: 'Inter', sans-serif;
        }

        .setup-container {
          width: 100%;
          max-width: 32rem; /* Slightly wider for better spacing */
          animation: fadeIn 0.8s ease-out;
        }

        .setup-form {
          background-color: white;
          border-radius: 1rem;
          padding: 2.5rem;
          box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08);
          border: 1px solid var(--border-color);
        }

        .form-header {
          text-align: center;
          margin-bottom: 2.5rem;
        }

        .header-icon {
          margin: 0 auto 1rem;
          color: var(--primary-color); /* Primary color for main icon */
          background-color: var(--primary-light);
          padding: 0.8rem;
          border-radius: 9999px;
          box-shadow: 0 4px 10px rgba(79, 70, 229, 0.2);
        }

        .form-title {
          font-size: 2rem;
          font-weight: 800;
          color: var(--text-dark);
          margin-bottom: 0.5rem;
          letter-spacing: -0.025em;
        }

        .form-subtitle {
          color: var(--text-light);
          font-size: 1rem;
          line-height: 1.5;
        }

        .form-group {
          margin-bottom: 1.75rem;
        }

        .form-label {
          display: block;
          font-size: 0.95rem;
          font-weight: 600;
          color: var(--text-dark);
          margin-bottom: 0.6rem;
        }

        .input-with-icon {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            transition: color 0.2s;
        }

        .form-input {
          width: 100%;
          padding: 0.9rem 1rem 0.9rem 3rem; /* Left padding for icon */
          border: 1px solid var(--border-color);
          border-radius: 0.6rem;
          font-size: 1rem;
          color: var(--text-medium);
          transition: all 0.2s ease-in-out;
          appearance: none; /* For select arrow */
        }

        .form-input:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
        }
        
        .form-input:focus + .input-icon {
            color: var(--primary-color);
        }

        .select-wrapper {
          position: relative;
        }

        .select-arrow {
          position: absolute;
          top: 50%;
          right: 1rem;
          transform: translateY(-50%);
          pointer-events: none;
          color: var(--text-light);
        }

        .select-arrow svg {
          width: 1.2rem;
          height: 1.2rem;
        }

        .photo-upload-area {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 120px;
            border: 2px dashed var(--border-color);
            border-radius: 0.75rem;
            background-color: var(--bg-light);
            transition: all 0.2s ease-in-out;
            padding: 1rem;
        }

        .photo-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--primary-light);
        }

        .upload-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--text-medium);
            font-weight: 500;
            font-size: 0.95rem;
            padding: 1rem;
            width: 100%;
            height: 100%;
        }

        .upload-icon {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .hidden-input {
            display: none;
        }

        .photo-preview {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            width: 100%;
        }

        .preview-image {
            width: 100px;
            height: 100px;
            border-radius: 9999px;
            object-fit: cover;
            border: 3px solid var(--primary-color);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .remove-photo-button {
            background-color: var(--red-error);
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 0.85rem;
            transition: background-color 0.2s;
        }

        .remove-photo-button:hover {
            background-color: #dc2626;
        }

        .submit-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          padding: 1rem;
          background-color: var(--primary-color);
          color: white;
          border: none;
          border-radius: 0.6rem;
          font-weight: 700;
          cursor: pointer;
          transition: all 0.3s ease-in-out;
          margin-top: 1.5rem;
          font-size: 1.1rem;
          box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
        }

        .submit-button:hover:not(:disabled) {
          background-color: var(--primary-dark);
          transform: translateY(-2px);
          box-shadow: 0 10px 25px rgba(79, 70, 229, 0.4);
        }

        .submit-button:disabled {
            background-color: #a78bfa; /* Lighter purple for disabled */
            cursor: not-allowed;
            opacity: 0.8;
        }

        .button-icon {
          margin-left: 0.75rem;
          transition: transform 0.2s;
        }

        .submit-button:hover:not(:disabled) .button-icon {
          transform: translateX(2px);
        }

        /* Message Box Styles */
        .message-box {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            border-radius: 0.6rem;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
            font-weight: 500;
        }

        .message-box.success {
            background-color: var(--green-light);
            color: var(--green-success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .message-box.error {
            background-color: var(--red-light);
            color: var(--red-error);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .message-box svg {
            flex-shrink: 0;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes animate-spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .animate-spin {
            animation: animate-spin 1s linear infinite;
        }

        /* Responsive Adjustments */
        @media (max-width: 640px) {
          .setup-form {
            padding: 1.5rem;
          }
          
          .form-title {
            font-size: 1.75rem;
          }

          .form-input {
            padding: 0.75rem 1rem 0.75rem 2.5rem;
          }

          .input-icon {
            left: 0.75rem;
          }

          .submit-button {
            padding: 0.85rem;
            font-size: 1rem;
          }

          .header-icon {
            padding: 0.6rem;
            width: 40px;
            height: 40px;
          }
        }
      `}</style>
    </div>
  );
};

export default ProfileSetup;
