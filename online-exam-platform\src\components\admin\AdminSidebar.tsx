import { NavLink } from 'react-router-dom';
import { 
  Gauge, 
  Users, 
  Shield, 
  Settings, 
  FileText, 
  LogOut 
} from 'lucide-react';

const AdminSidebar = () => {
  const links = [
    { to: '/admin/dashboard', label: 'Dashboard', icon: <Gauge size={18} /> },
    { to: '/admin/users', label: 'Users', icon: <Users size={18} /> },
    { to: '/admin/roles', label: 'Roles & Permissions', icon: <Shield size={18} /> },
    { to: '/admin/settings', label: 'Settings', icon: <Settings size={18} /> },
    { to: '/admin/logs', label: 'Audit Logs', icon: <FileText size={18} /> },
    { to: '/login', label: 'Logout', icon: <LogOut size={18} /> },
  ];

  return (
    <div className="sidebar-container">
      <div className="sidebar-header">
        <h1 className="sidebar-title">Admin Panel</h1>
      </div>
      <nav className="sidebar-nav">
        {links.map((item, idx) => (
          <NavLink
            key={idx}
            to={item.to}
            className={({ isActive }) => `sidebar-link ${isActive ? 'active' : ''}`}
          >
            <span className="sidebar-icon">{item.icon}</span>
            <span className="sidebar-label">{item.label}</span>
          </NavLink>
        ))}
      </nav>

      <style>{`
        .sidebar-container {
          width: 16rem;
          background-color: #1e293b;
          color: white;
          min-height: 100vh;
          display: flex;
          flex-direction: column;
        }
        .sidebar-header {
          padding: 1.5rem;
          border-bottom: 1px solid #334155;
        }
        .sidebar-title {
          font-size: 1.25rem;
          font-weight: 700;
        }
        .sidebar-nav {
          flex: 1;
          padding: 0.5rem 0;
          display: flex;
          flex-direction: column;
        }
        .sidebar-link {
          display: flex;
          align-items: center;
          padding: 0.75rem 1.5rem;
          margin: 0.25rem 0.5rem;
          border-radius: 0.375rem;
          color: #e2e8f0;
          text-decoration: none;
          transition: all 0.2s ease;
        }
        .sidebar-link:hover {
          background-color: #334155;
          color: white;
        }
        .sidebar-link.active {
          background-color: #4f46e5;
          color: white;
        }
        .sidebar-icon {
          margin-right: 0.75rem;
        }
      `}</style>
    </div>
  );
};

export default AdminSidebar;
