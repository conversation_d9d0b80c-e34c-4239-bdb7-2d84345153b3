import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Lock, Clock, FileText, UserCircle, Camera, Layout, CheckCircle } from 'lucide-react';

const ExamInstructions = () => {
  const { examId } = useParams();
  const [timeRemaining, setTimeRemaining] = useState(300); // 5 minutes for demo
  const [idVerified, setIdVerified] = useState(false);
  const [rulesAccepted, setRulesAccepted] = useState(false);
  const [environmentChecked, setEnvironmentChecked] = useState(false);
  const [idImage, setIdImage] = useState<string | null>(null);
  const [showVerificationModal, setShowVerificationModal] = useState(false);

  // Mock exam data - in a real app this would come from an API
  const examData: ExamDataMap = {
    'js101': {
      title: 'JavaScript Fundamentals Certification',
      duration: 60,
      totalQuestions: 30,
      passingScore: 70,
      rules: [
        'No external resources or websites allowed',
        'Camera must remain on throughout the exam',
        'Microphone access required for proctoring',
        'No switching tabs or applications during the exam',
        'All answers are final once submitted'
      ],
      materials: 'Pen, paper, and a calculator are allowed'
    },
    'py202': {
      title: 'Python Advanced Concepts',
      duration: 90,
      totalQuestions: 45,
      passingScore: 75,
      rules: [
        'No internet access allowed',
        'Screen sharing will be monitored',
        'Environment scan required before starting',
        'No communication with others during exam',
        'Random identity verification checks may occur'
      ],
      materials: 'Only a physical textbook is permitted'
    }
  };

  const exam = examData[examId as string] || examData['js101'];

  // Countdown timer effect
  useEffect(() => {
    if (timeRemaining > 0) {
      const timer = setTimeout(() => setTimeRemaining(timeRemaining - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [timeRemaining]);

interface ExamData {
    title: string;
    duration: number;
    totalQuestions: number;
    passingScore: number;
    rules: string[];
    materials: string;
}

interface ExamDataMap {
    [key: string]: ExamData;
}

const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
};

interface IDUploadEvent extends React.ChangeEvent<HTMLInputElement> {}

const handleIDUpload = (e: IDUploadEvent) => {
    const file = e.target.files && e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = (event: ProgressEvent<FileReader>) => {
            const result = event.target?.result;
            if (typeof result === 'string') {
                setIdImage(result);
                // In a real app, this would call a verification API
                setTimeout(() => {
                    setShowVerificationModal(true);
                    setIdVerified(true);
                }, 1500);
            }
        };
        reader.readAsDataURL(file);
    }
};

  const startEnvironmentCheck = () => {
    // In a real app, this would use the device API to check camera/mic
    setTimeout(() => {
      setEnvironmentChecked(true);
    }, 2000);
  };

  const allRequirementsMet = idVerified && rulesAccepted && environmentChecked;

  return (
    <div className="exam-instructions-container">
      <div className="exam-instructions-content">
        <div className="exam-header">
          <h1>Instructions for {exam.title}</h1>
          <div className="time-remaining">
            <Clock className="icon" />
            <span>Time to start: {formatTime(timeRemaining)}</span>
          </div>
        </div>

        <div className="exam-details-grid">
          <div className="details-card">
            <h2>
              <FileText className="icon" />
              Exam Details
            </h2>
            <ul>
              <li><strong>Duration:</strong> {exam.duration} minutes</li>
              <li><strong>Questions:</strong> {exam.totalQuestions}</li>
              <li><strong>Passing Score:</strong> {exam.passingScore}%</li>
              <li><strong>Allowed Materials:</strong> {exam.materials}</li>
            </ul>
          </div>

          <div className="rules-card">
            <h2>
              <Lock className="icon" />
              Exam Rules
            </h2>
            <ul>
              {exam.rules.map((rule: string, index: number) => (
                <li key={index}>{rule}</li>
              ))}
            </ul>
            <div className="checkbox-group">
              <input
                type="checkbox"
                id="acceptRules"
                checked={rulesAccepted}
                onChange={(e) => setRulesAccepted(e.target.checked)}
              />
              <label htmlFor="acceptRules">
                I understand and accept all exam rules
              </label>
            </div>
          </div>

          <div className="verification-card">
            <h2>
              <UserCircle className="icon" />
              Identity Verification
            </h2>
            <div className="upload-section">
              {idImage ? (
                <div className="id-preview">
                  <img src={idImage} alt="Uploaded ID" />
                  {idVerified && (
                    <div className="verification-badge">
                      <CheckCircle className="icon" />
                      Verified
                    </div>
                  )}
                </div>
              ) : (
                <div className="upload-box">
                  <Camera className="icon" />
                  <p>Upload a clear photo of your government-issued ID</p>
                  <input
                    type="file"
                    id="idUpload"
                    accept="image/*"
                    onChange={handleIDUpload}
                    className="hidden"
                  />
                  <label htmlFor="idUpload" className="upload-button">
                    Choose File
                  </label>
                </div>
              )}
            </div>
          </div>

          <div className="environment-card">
            <h2>
              <Layout className="icon" />
              Environment Check
            </h2>
            <p>Before starting, we need to verify your testing environment:</p>
            <ul>
              <li>Camera access</li>
              <li>Microphone access</li>
              <li>Screen sharing permission</li>
              <li>Clear workspace</li>
            </ul>
            <button
              className={`check-button ${environmentChecked ? 'verified' : ''}`}
              onClick={startEnvironmentCheck}
              disabled={environmentChecked}
            >
              {environmentChecked ? 'Environment Verified' : 'Start Environment Check'}
            </button>
          </div>
        </div>

        <div className="start-exam-section">
          <button
            className={`start-button ${!allRequirementsMet ? 'disabled' : ''}`}
            disabled={!allRequirementsMet}
            onClick={() => window.location.href = `/student/exam/${examId}/take`}
          >
            Start Exam Now
          </button>
          {!allRequirementsMet && (
            <p className="requirements-message">
              Please complete all verification steps before starting
            </p>
          )}
        </div>
      </div>

      {/* Verification Modal */}
      {showVerificationModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-content">
              <h2>Identity Verified Successfully</h2>
              <p>Your ID has been verified and matches our records.</p>
              <div className="id-display">
                <img src={idImage || undefined} alt="Verified ID" />
              </div>
              <p className="note">This information will be used for proctoring during your exam.</p>
            </div>
            <div className="modal-footer">
              <button
                onClick={() => setShowVerificationModal(false)}
                className="confirm-button"
              >
                Continue
              </button>
            </div>
          </div>
        </div>
      )}

      <style>{`
        .exam-instructions-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          padding: 2rem;
        }

        .exam-instructions-content {
          max-width: 1200px;
          margin: 0 auto;
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(0, 0, 0, 0.05);
          padding: 2rem;
        }

        .exam-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #e5e7eb;
        }

        .exam-header h1 {
          font-size: 1.5rem;
          font-weight: 700;
          color: #1f2937;
          margin: 0;
        }

        .time-remaining {
          display: flex;
          align-items: center;
          background: #fef3c7;
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
          font-weight: 500;
          color: #92400e;
        }

        .time-remaining .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
        }

        .exam-details-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 1.5rem;
          margin-bottom: 2rem;
        }

        .details-card,
        .rules-card,
        .verification-card,
        .environment-card {
          background: #f9fafb;
          border-radius: 0.5rem;
          padding: 1.5rem;
          border: 1px solid #e5e7eb;
        }

        .details-card h2,
        .rules-card h2,
        .verification-card h2,
        .environment-card h2 {
          display: flex;
          align-items: center;
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 1rem 0;
        }

        .details-card .icon,
        .rules-card .icon,
        .verification-card .icon,
        .environment-card .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
          color: #4f46e5;
        }

        .details-card ul,
        .rules-card ul,
        .environment-card ul {
          margin: 0;
          padding-left: 1.25rem;
        }

        .details-card li,
        .rules-card li,
        .environment-card li {
          margin-bottom: 0.5rem;
          font-size: 0.875rem;
          color: #4b5563;
        }

        .rules-card li {
          position: relative;
          padding-left: 1.5rem;
        }

        .rules-card li:before {
          content: '•';
          position: absolute;
          left: 0;
          color: #ef4444;
          font-weight: bold;
        }

        .checkbox-group {
          display: flex;
          align-items: center;
          margin-top: 1rem;
        }

        .checkbox-group input {
          margin-right: 0.5rem;
        }

        .checkbox-group label {
          font-size: 0.875rem;
          color: #4b5563;
          cursor: pointer;
        }

        .upload-section {
          margin-top: 1rem;
        }

        .upload-box {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 2rem;
          border: 2px dashed #d1d5db;
          border-radius: 0.375rem;
          text-align: center;
        }

        .upload-box .icon {
          width: 2rem;
          height: 2rem;
          color: #9ca3af;
          margin-bottom: 0.5rem;
        }

        .upload-box p {
          font-size: 0.875rem;
          color: #6b7280;
          margin: 0 0 1rem 0;
        }

        .upload-button {
          padding: 0.5rem 1rem;
          background: #4f46e5;
          color: white;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          cursor: pointer;
          transition: background 0.2s;
        }

        .upload-button:hover {
          background: #4338ca;
        }

        .id-preview {
          position: relative;
          border: 1px solid #e5e7eb;
          border-radius: 0.375rem;
          overflow: hidden;
          height: 180px;
        }

        .id-preview img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .verification-badge {
          position: absolute;
          bottom: 0.5rem;
          right: 0.5rem;
          display: flex;
          align-items: center;
          padding: 0.25rem 0.75rem;
          background: #dcfce7;
          color: #166534;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 600;
        }

        .verification-badge .icon {
          width: 1rem;
          height: 1rem;
          margin-right: 0.25rem;
        }

        .check-button {
          width: 100%;
          padding: 0.75rem;
          margin-top: 1rem;
          background: #f3f4f6;
          color: #4b5563;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .check-button:hover:not(:disabled) {
          background: #e5e7eb;
        }

        .check-button.verified {
          background: #dcfce7;
          color: #166534;
          cursor: default;
        }

        .check-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .start-exam-section {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-top: 2rem;
          padding-top: 1.5rem;
          border-top: 1px solid #e5e7eb;
        }

        .start-button {
          padding: 0.75rem 2rem;
          background: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-size: 1rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .start-button:hover:not(.disabled) {
          background: #4338ca;
        }

        .start-button.disabled {
          background: #9ca3af;
          cursor: not-allowed;
        }

        .requirements-message {
          font-size: 0.875rem;
          color: #ef4444;
          margin-top: 0.5rem;
        }

        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal {
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          width: 100%;
          max-width: 500px;
          overflow: hidden;
        }

        .modal-content {
          padding: 1.5rem;
          text-align: center;
        }

        .modal-content h2 {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1rem;
        }

        .modal-content p {
          color: #6b7280;
          margin-bottom: 1.5rem;
        }

        .id-display {
          background: #f9fafb;
          border-radius: 0.375rem;
          padding: 1rem;
          margin-bottom: 1.5rem;
          height: 200px;
          overflow: hidden;
        }

        .id-display img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }

        .note {
          font-size: 0.75rem;
          color: #6b7280;
        }

        .modal-footer {
          display: flex;
          justify-content: center;
          padding: 1rem 1.5rem;
          background: #f9fafb;
          border-top: 1px solid #e5e7eb;
        }

        .confirm-button {
          padding: 0.5rem 1.5rem;
          background: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .confirm-button:hover {
          background: #4338ca;
        }

        @media (max-width: 768px) {
          .exam-instructions-content {
            padding: 1.5rem;
          }

          .exam-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
          }

          .exam-details-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default ExamInstructions;